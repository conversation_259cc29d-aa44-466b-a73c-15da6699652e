<?php
/**
 * API Router for Sagay Cemetery Grave Locator
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Set CORS headers
setCORSHeaders();

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get the request URI and method
$request_uri = $_SERVER['REQUEST_URI'];
$request_method = $_SERVER['REQUEST_METHOD'];

// Remove query string and base path
$path = parse_url($request_uri, PHP_URL_PATH);
$path = str_replace('/ngeks/api', '', $path);
$path = trim($path, '/');

// Split path into segments
$segments = explode('/', $path);
$endpoint = $segments[0] ?? '';
$id = $segments[1] ?? null;

// Route the request
try {
    switch ($endpoint) {
        case 'graves':
            require_once 'endpoints/graves.php';
            handleGravesRequest($request_method, $id);
            break;
            
        case 'search':
            require_once 'endpoints/search.php';
            handleSearchRequest($request_method);
            break;
            
        case 'sections':
            require_once 'endpoints/sections.php';
            handleSectionsRequest($request_method, $id);
            break;
            
        case 'admin':
            require_once 'endpoints/admin.php';
            handleAdminRequest($request_method, $segments);
            break;
            
        case 'stats':
            require_once 'endpoints/stats.php';
            handleStatsRequest($request_method);
            break;
            
        case 'feedback':
            require_once 'endpoints/feedback.php';
            handleFeedbackRequest($request_method, $id);
            break;
            
        default:
            sendResponse(404, ['error' => 'Endpoint not found']);
            break;
    }
} catch (Exception $e) {
    if (DEBUG_MODE) {
        sendResponse(500, [
            'error' => 'Internal server error',
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    } else {
        sendResponse(500, ['error' => 'Internal server error']);
    }
}

/**
 * Send JSON response
 */
function sendResponse($status_code, $data) {
    http_response_code($status_code);
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit();
}
?>
