<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sagay Public Cemetery - Grave Locator</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Cemetery Locator">
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/favicon-16x16.png">
    <link rel="apple-touch-icon" href="assets/images/apple-touch-icon.png">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Styles -->
    <link rel="stylesheet" href="assets/css/mobile.css">
    
    <!-- Google Maps API -->
    <script async defer src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&libraries=geometry"></script>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p>Loading Cemetery Locator...</p>
    </div>

    <!-- Main App Container -->
    <div id="app" class="app-container" style="display: none;">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1>Sagay Public Cemetery</h1>
                <p>Grave Locator System</p>
            </div>
            <button id="menu-btn" class="menu-btn">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </header>

        <!-- Navigation Menu -->
        <nav id="nav-menu" class="nav-menu">
            <ul>
                <li><a href="#" data-page="search" class="nav-link active">Search Graves</a></li>
                <li><a href="#" data-page="map" class="nav-link">Cemetery Map</a></li>
                <li><a href="#" data-page="qr" class="nav-link">QR Scanner</a></li>
                <li><a href="#" data-page="about" class="nav-link">About</a></li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Search Page -->
            <div id="search-page" class="page active">
                <div class="search-container">
                    <div class="search-box">
                        <input type="text" id="search-input" placeholder="Enter name or grave number..." autocomplete="off">
                        <button id="search-btn" class="search-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="search-filters">
                        <select id="search-type">
                            <option value="name">Search by Name</option>
                            <option value="grave_number">Search by Grave Number</option>
                        </select>
                    </div>
                </div>

                <!-- Search Results -->
                <div id="search-results" class="search-results"></div>
                
                <!-- No Results Message -->
                <div id="no-results" class="no-results" style="display: none;">
                    <div class="no-results-icon">🔍</div>
                    <h3>No graves found</h3>
                    <p>Try adjusting your search terms or check the spelling.</p>
                </div>
            </div>

            <!-- Map Page -->
            <div id="map-page" class="page">
                <div class="map-controls">
                    <button id="locate-btn" class="control-btn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                            <circle cx="12" cy="10" r="3"></circle>
                        </svg>
                        My Location
                    </button>
                    <button id="cemetery-center-btn" class="control-btn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                            <path d="M2 17l10 5 10-5"></path>
                            <path d="M2 12l10 5 10-5"></path>
                        </svg>
                        Cemetery Center
                    </button>
                </div>
                <div id="map-container" class="map-container"></div>
            </div>

            <!-- QR Scanner Page -->
            <div id="qr-page" class="page">
                <div class="qr-scanner-container">
                    <h2>QR Code Scanner</h2>
                    <p>Point your camera at a grave's QR code to get instant information.</p>
                    
                    <div id="qr-scanner" class="qr-scanner">
                        <video id="qr-video" autoplay muted playsinline></video>
                        <div class="qr-overlay">
                            <div class="qr-frame"></div>
                        </div>
                    </div>
                    
                    <div class="qr-controls">
                        <button id="start-scan-btn" class="btn btn-primary">Start Scanning</button>
                        <button id="stop-scan-btn" class="btn btn-secondary" style="display: none;">Stop Scanning</button>
                    </div>
                    
                    <div class="qr-manual-input">
                        <p>Or enter QR code manually:</p>
                        <input type="text" id="qr-input" placeholder="Enter QR code...">
                        <button id="qr-search-btn" class="btn btn-primary">Search</button>
                    </div>
                </div>
            </div>

            <!-- About Page -->
            <div id="about-page" class="page">
                <div class="about-content">
                    <h2>About Sagay Public Cemetery</h2>
                    <p>The Sagay Public Cemetery Grave Locator System helps visitors find graves quickly and efficiently using modern GPS technology.</p>
                    
                    <div class="features-list">
                        <h3>Features:</h3>
                        <ul>
                            <li>🔍 Search graves by name or number</li>
                            <li>📍 GPS navigation to exact locations</li>
                            <li>🗺️ Interactive cemetery map</li>
                            <li>📱 QR code scanning</li>
                            <li>📶 Works offline for basic searches</li>
                        </ul>
                    </div>
                    
                    <div class="contact-info">
                        <h3>Contact Information:</h3>
                        <p>📞 Phone: +63-34-XXX-XXXX</p>
                        <p>📧 Email: <EMAIL></p>
                        <p>🕒 Hours: 6:00 AM - 6:00 PM</p>
                    </div>
                </div>
            </div>
        </main>

        <!-- Grave Details Modal -->
        <div id="grave-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modal-title">Grave Details</h2>
                    <button id="close-modal" class="close-btn">&times;</button>
                </div>
                <div id="modal-body" class="modal-body">
                    <!-- Content will be populated by JavaScript -->
                </div>
                <div class="modal-footer">
                    <button id="navigate-btn" class="btn btn-primary">Navigate to Grave</button>
                    <button id="close-modal-btn" class="btn btn-secondary">Close</button>
                </div>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div id="toast-container" class="toast-container"></div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/app.js"></script>
    <script src="assets/js/search.js"></script>
    <script src="assets/js/map.js"></script>
    <script src="assets/js/qr-scanner.js"></script>
    
    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('sw.js')
                    .then(registration => console.log('SW registered'))
                    .catch(error => console.log('SW registration failed'));
            });
        }
    </script>
</body>
</html>
