<?php
/**
 * Installation Script for Sagay Cemetery Grave Locator
 */

// Check if already installed
if (file_exists('config/installed.lock')) {
    die('System is already installed. Delete config/installed.lock to reinstall.');
}

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 2:
            // Database configuration
            $db_config = [
                'host' => $_POST['db_host'] ?? 'localhost',
                'name' => $_POST['db_name'] ?? 'sagay_cemetery',
                'username' => $_POST['db_username'] ?? 'root',
                'password' => $_POST['db_password'] ?? ''
            ];
            
            // Test database connection
            try {
                $pdo = new PDO(
                    "mysql:host={$db_config['host']};dbname={$db_config['name']}",
                    $db_config['username'],
                    $db_config['password']
                );
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Save database config
                $config_content = "<?php\n";
                $config_content .= "// Database Configuration - Generated by installer\n";
                $config_content .= "class Database {\n";
                $config_content .= "    private \$host = '{$db_config['host']}';\n";
                $config_content .= "    private \$db_name = '{$db_config['name']}';\n";
                $config_content .= "    private \$username = '{$db_config['username']}';\n";
                $config_content .= "    private \$password = '{$db_config['password']}';\n";
                $config_content .= "    private \$conn;\n\n";
                $config_content .= "    public function getConnection() {\n";
                $config_content .= "        \$this->conn = null;\n";
                $config_content .= "        try {\n";
                $config_content .= "            \$this->conn = new PDO(\n";
                $config_content .= "                \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name,\n";
                $config_content .= "                \$this->username,\n";
                $config_content .= "                \$this->password,\n";
                $config_content .= "                array(\n";
                $config_content .= "                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,\n";
                $config_content .= "                    PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES utf8\",\n";
                $config_content .= "                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC\n";
                $config_content .= "                )\n";
                $config_content .= "            );\n";
                $config_content .= "        } catch(PDOException \$exception) {\n";
                $config_content .= "            echo \"Connection error: \" . \$exception->getMessage();\n";
                $config_content .= "        }\n";
                $config_content .= "        return \$this->conn;\n";
                $config_content .= "    }\n";
                $config_content .= "}\n\n";
                $config_content .= "function getDBConnection() {\n";
                $config_content .= "    \$database = new Database();\n";
                $config_content .= "    return \$database->getConnection();\n";
                $config_content .= "}\n";
                $config_content .= "?>";
                
                file_put_contents('config/database.php', $config_content);
                
                $success = 'Database connection successful!';
                $step = 3;
            } catch (Exception $e) {
                $error = 'Database connection failed: ' . $e->getMessage();
            }
            break;
            
        case 3:
            // Import database schema
            try {
                require_once 'config/database.php';
                $db = getDBConnection();
                
                $schema = file_get_contents('database/schema.sql');
                $statements = explode(';', $schema);
                
                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement)) {
                        $db->exec($statement);
                    }
                }
                
                $success = 'Database schema imported successfully!';
                $step = 4;
            } catch (Exception $e) {
                $error = 'Schema import failed: ' . $e->getMessage();
            }
            break;
            
        case 4:
            // Create admin user
            $admin_data = [
                'username' => $_POST['admin_username'] ?? '',
                'email' => $_POST['admin_email'] ?? '',
                'password' => $_POST['admin_password'] ?? '',
                'full_name' => $_POST['admin_name'] ?? ''
            ];
            
            if (empty($admin_data['username']) || empty($admin_data['password'])) {
                $error = 'Username and password are required.';
            } else {
                try {
                    require_once 'config/database.php';
                    $db = getDBConnection();
                    
                    $password_hash = password_hash($admin_data['password'], PASSWORD_DEFAULT);
                    
                    $stmt = $db->prepare("
                        INSERT INTO admin_users (username, email, password_hash, full_name, role) 
                        VALUES (?, ?, ?, ?, 'super_admin')
                    ");
                    
                    $stmt->execute([
                        $admin_data['username'],
                        $admin_data['email'],
                        $password_hash,
                        $admin_data['full_name']
                    ]);
                    
                    $success = 'Admin user created successfully!';
                    $step = 5;
                } catch (Exception $e) {
                    $error = 'Failed to create admin user: ' . $e->getMessage();
                }
            }
            break;
            
        case 5:
            // Finalize installation
            $maps_api_key = $_POST['maps_api_key'] ?? '';
            
            // Update config with API key
            $config_file = 'config/config.php';
            $config_content = file_get_contents($config_file);
            $config_content = str_replace('YOUR_GOOGLE_MAPS_API_KEY_HERE', $maps_api_key, $config_content);
            file_put_contents($config_file, $config_content);
            
            // Create installation lock file
            file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
            
            // Create uploads directory
            if (!is_dir('uploads')) {
                mkdir('uploads', 0755, true);
            }
            
            $success = 'Installation completed successfully!';
            $step = 6;
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install - Sagay Cemetery Grave Locator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .install-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 600px;
            width: 100%;
            overflow: hidden;
        }
        .install-header {
            background: #2c3e50;
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .install-content {
            padding: 2rem;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #ecf0f1;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 5px;
            font-weight: bold;
        }
        .step.active {
            background: #3498db;
            color: white;
        }
        .step.completed {
            background: #27ae60;
            color: white;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
        }
        .form-group input:focus {
            outline: none;
            border-color: #3498db;
        }
        .btn {
            background: #3498db;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
        }
        .btn:hover {
            background: #2980b9;
        }
        .error {
            background: #fdf2f2;
            color: #e74c3c;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .success {
            background: #f0f9f4;
            color: #27ae60;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .requirements {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .requirements ul {
            margin: 0;
            padding-left: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>Sagay Cemetery Grave Locator</h1>
            <p>Installation Wizard</p>
        </div>
        
        <div class="install-content">
            <div class="step-indicator">
                <?php for ($i = 1; $i <= 6; $i++): ?>
                    <div class="step <?php echo $i < $step ? 'completed' : ($i == $step ? 'active' : ''); ?>">
                        <?php echo $i; ?>
                    </div>
                <?php endfor; ?>
            </div>
            
            <?php if ($error): ?>
                <div class="error"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="success"><?php echo htmlspecialchars($success); ?></div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <h2>Welcome</h2>
                <p>This wizard will help you install the Sagay Cemetery Grave Locator System.</p>
                
                <div class="requirements">
                    <h3>Requirements:</h3>
                    <ul>
                        <li>PHP 7.4 or higher</li>
                        <li>MySQL 5.7 or higher</li>
                        <li>Apache web server</li>
                        <li>Google Maps API key (optional)</li>
                    </ul>
                </div>
                
                <a href="?step=2" class="btn">Start Installation</a>
                
            <?php elseif ($step == 2): ?>
                <h2>Database Configuration</h2>
                <form method="POST">
                    <div class="form-group">
                        <label>Database Host:</label>
                        <input type="text" name="db_host" value="localhost" required>
                    </div>
                    <div class="form-group">
                        <label>Database Name:</label>
                        <input type="text" name="db_name" value="sagay_cemetery" required>
                    </div>
                    <div class="form-group">
                        <label>Database Username:</label>
                        <input type="text" name="db_username" value="root" required>
                    </div>
                    <div class="form-group">
                        <label>Database Password:</label>
                        <input type="password" name="db_password">
                    </div>
                    <button type="submit" class="btn">Test Connection</button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <h2>Import Database Schema</h2>
                <p>Click the button below to import the database schema.</p>
                <form method="POST">
                    <button type="submit" class="btn">Import Schema</button>
                </form>
                
            <?php elseif ($step == 4): ?>
                <h2>Create Admin User</h2>
                <form method="POST">
                    <div class="form-group">
                        <label>Username:</label>
                        <input type="text" name="admin_username" required>
                    </div>
                    <div class="form-group">
                        <label>Email:</label>
                        <input type="email" name="admin_email" required>
                    </div>
                    <div class="form-group">
                        <label>Full Name:</label>
                        <input type="text" name="admin_name" required>
                    </div>
                    <div class="form-group">
                        <label>Password:</label>
                        <input type="password" name="admin_password" required>
                    </div>
                    <button type="submit" class="btn">Create Admin User</button>
                </form>
                
            <?php elseif ($step == 5): ?>
                <h2>Final Configuration</h2>
                <form method="POST">
                    <div class="form-group">
                        <label>Google Maps API Key (optional):</label>
                        <input type="text" name="maps_api_key" placeholder="Enter your Google Maps API key">
                        <small>You can add this later in config/config.php</small>
                    </div>
                    <button type="submit" class="btn">Complete Installation</button>
                </form>
                
            <?php elseif ($step == 6): ?>
                <h2>Installation Complete!</h2>
                <p>The Sagay Cemetery Grave Locator System has been installed successfully.</p>
                
                <div class="success">
                    <h3>Next Steps:</h3>
                    <ul>
                        <li>Access the mobile app at: <a href="index.html">index.html</a></li>
                        <li>Access the admin panel at: <a href="admin/">admin/</a></li>
                        <li>Configure your Google Maps API key in config/config.php</li>
                        <li>Add cemetery sections and graves through the admin panel</li>
                    </ul>
                </div>
                
                <a href="admin/" class="btn">Go to Admin Panel</a>
                
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
