<?php
/**
 * Quick Database Setup for Sagay Cemetery System
 * Run this if you're getting database connection errors
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sagay_cemetery';

echo "<h2>Sagay Cemetery - Database Setup</h2>";

try {
    // Step 1: Connect to MySQL server (without specifying database)
    echo "<p>Step 1: Connecting to MySQL server...</p>";
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Connected to MySQL server successfully!</p>";
    
    // Step 2: Create database if it doesn't exist
    echo "<p>Step 2: Creating database '$database'...</p>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✓ Database '$database' created/verified successfully!</p>";
    
    // Step 3: Connect to the specific database
    echo "<p>Step 3: Connecting to database '$database'...</p>";
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Connected to database '$database' successfully!</p>";
    
    // Step 4: Check if tables exist
    echo "<p>Step 4: Checking database tables...</p>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<p style='color: orange;'>⚠ No tables found. You need to import the database schema.</p>";
        echo "<p><strong>Next steps:</strong></p>";
        echo "<ol>";
        echo "<li><a href='install.php'>Run the Installation Wizard</a> (Recommended)</li>";
        echo "<li>Or manually import 'database/schema.sql' via phpMyAdmin</li>";
        echo "</ol>";
    } else {
        echo "<p style='color: green;'>✓ Found " . count($tables) . " tables: " . implode(', ', $tables) . "</p>";
        
        // Check for admin users
        if (in_array('admin_users', $tables)) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_users");
            $result = $stmt->fetch();
            
            if ($result['count'] > 0) {
                echo "<p style='color: green;'>✓ Admin users found. System appears to be set up!</p>";
                echo "<p><strong>You can now access:</strong></p>";
                echo "<ul>";
                echo "<li><a href='index.html'>Mobile App</a></li>";
                echo "<li><a href='admin/'>Admin Panel</a></li>";
                echo "<li><a href='test.php'>Run System Tests</a></li>";
                echo "</ul>";
            } else {
                echo "<p style='color: orange;'>⚠ No admin users found.</p>";
                echo "<p><a href='install.php'>Complete the installation</a> to create an admin user.</p>";
            }
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
    
    echo "<h3>Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li><strong>Check XAMPP:</strong> Make sure Apache and MySQL are running in XAMPP Control Panel</li>";
    echo "<li><strong>Check credentials:</strong> Default XAMPP MySQL username is 'root' with no password</li>";
    echo "<li><strong>Check MySQL port:</strong> Default is 3306, make sure it's not blocked</li>";
    echo "<li><strong>Check permissions:</strong> MySQL user needs CREATE DATABASE privileges</li>";
    echo "</ul>";
    
    echo "<h3>Manual Setup:</h3>";
    echo "<ol>";
    echo "<li>Open phpMyAdmin: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>";
    echo "<li>Create a new database named 'sagay_cemetery'</li>";
    echo "<li>Set collation to 'utf8mb4_unicode_ci'</li>";
    echo "<li>Import the file 'database/schema.sql'</li>";
    echo "<li><a href='setup_database.php'>Refresh this page</a></li>";
    echo "</ol>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Database Setup - Sagay Cemetery</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 50px auto; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 { 
            color: #2c3e50; 
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h3 { 
            color: #2c3e50; 
            margin-top: 30px;
        }
        a { 
            color: #3498db; 
            text-decoration: none; 
            font-weight: bold;
        }
        a:hover { 
            text-decoration: underline; 
        }
        ul, ol { 
            margin: 10px 0; 
            padding-left: 30px;
        }
        li { 
            margin: 5px 0; 
        }
        .actions {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="actions">
            <h3>Quick Actions:</h3>
            <a href="setup_database.php" class="btn">Refresh Status</a>
            <a href="install.php" class="btn">Installation Wizard</a>
            <a href="http://localhost/phpmyadmin" class="btn" target="_blank">Open phpMyAdmin</a>
            <a href="test.php" class="btn">Test System</a>
        </div>
    </div>
</body>
</html>
