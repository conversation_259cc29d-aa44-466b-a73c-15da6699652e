<?php
/**
 * Quick Database Setup for Sagay Cemetery System
 * Run this if you're getting database connection errors
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Sagay Cemetery - Database Setup</h2>";

// Database configuration - you can modify these if needed
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sagay_cemetery';

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>Current Configuration:</h3>";
echo "<p><strong>Host:</strong> $host</p>";
echo "<p><strong>Username:</strong> $username</p>";
echo "<p><strong>Password:</strong> " . (empty($password) ? '(empty)' : '(set)') . "</p>";
echo "<p><strong>Database:</strong> $database</p>";
echo "</div>";

// Step 1: Check if MySQL extension is loaded
echo "<p><strong>Step 1:</strong> Checking PHP MySQL support...</p>";
if (!extension_loaded('pdo') || !extension_loaded('pdo_mysql')) {
    echo "<p style='color: red;'>✗ PDO or PDO_MySQL extension not loaded!</p>";
    echo "<p>Please enable PDO and PDO_MySQL in your PHP configuration.</p>";
    exit;
} else {
    echo "<p style='color: green;'>✓ PDO and PDO_MySQL extensions are loaded</p>";
}

// Step 2: Test basic MySQL connection
echo "<p><strong>Step 2:</strong> Testing MySQL server connection...</p>";
try {
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Connected to MySQL server successfully!</p>";

    // Get MySQL version
    $version = $pdo->query('SELECT VERSION()')->fetchColumn();
    echo "<p><strong>MySQL Version:</strong> $version</p>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Cannot connect to MySQL server!</p>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";

    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🔧 Troubleshooting Steps:</h3>";
    echo "<ol>";
    echo "<li><strong>Check XAMPP Control Panel:</strong><br>";
    echo "   - Make sure Apache and MySQL are both running (green status)<br>";
    echo "   - If MySQL is red, click 'Start' next to MySQL</li>";
    echo "<li><strong>Check MySQL Port:</strong><br>";
    echo "   - Default port is 3306<br>";
    echo "   - Make sure no other program is using this port</li>";
    echo "<li><strong>Try different credentials:</strong><br>";
    echo "   - Some XAMPP installations use different defaults<br>";
    echo "   - Try username: 'root', password: 'root' or 'mysql'</li>";
    echo "<li><strong>Restart XAMPP:</strong><br>";
    echo "   - Stop both Apache and MySQL<br>";
    echo "   - Wait 5 seconds, then start them again</li>";
    echo "</ol>";
    echo "</div>";

    // Show form to try different credentials
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>Try Different Database Credentials:</h3>";
    echo "<form method='POST'>";
    echo "<p><label>Host: <input type='text' name='host' value='$host' style='margin-left: 10px; padding: 5px;'></label></p>";
    echo "<p><label>Username: <input type='text' name='username' value='$username' style='margin-left: 10px; padding: 5px;'></label></p>";
    echo "<p><label>Password: <input type='password' name='password' value='$password' style='margin-left: 10px; padding: 5px;'></label></p>";
    echo "<p><input type='submit' value='Test Connection' style='background: #007cba; color: white; padding: 8px 15px; border: none; border-radius: 3px; cursor: pointer;'></p>";
    echo "</form>";
    echo "</div>";

    exit;
}

// Handle form submission for different credentials
if ($_POST) {
    $host = $_POST['host'] ?? $host;
    $username = $_POST['username'] ?? $username;
    $password = $_POST['password'] ?? $password;
    echo "<p style='color: blue;'>ℹ Using submitted credentials...</p>";
}

// Step 3: Create database if it doesn't exist
echo "<p><strong>Step 3:</strong> Creating/checking database '$database'...</p>";
try {
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✓ Database '$database' created/verified successfully!</p>";
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Cannot create database: " . $e->getMessage() . "</p>";
    echo "<p>You may need to create the database manually via phpMyAdmin.</p>";
}

// Step 4: Connect to the specific database
echo "<p><strong>Step 4:</strong> Connecting to database '$database'...</p>";
try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Connected to database '$database' successfully!</p>";
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Cannot connect to database '$database': " . $e->getMessage() . "</p>";
    exit;
}

// Step 5: Check if tables exist
echo "<p><strong>Step 5:</strong> Checking database tables...</p>";
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

    if (empty($tables)) {
        echo "<p style='color: orange;'>⚠ No tables found. Database is empty.</p>";

        // Try to import schema automatically
        $schema_file = 'database/schema.sql';
        if (file_exists($schema_file)) {
            echo "<p><strong>Step 6:</strong> Importing database schema...</p>";
            try {
                $schema = file_get_contents($schema_file);
                $statements = explode(';', $schema);

                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement) && !preg_match('/^(CREATE DATABASE|USE)/i', $statement)) {
                        $pdo->exec($statement);
                    }
                }

                echo "<p style='color: green;'>✓ Database schema imported successfully!</p>";

                // Recheck tables
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                echo "<p style='color: green;'>✓ Found " . count($tables) . " tables: " . implode(', ', $tables) . "</p>";

            } catch (PDOException $e) {
                echo "<p style='color: red;'>✗ Failed to import schema: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ Schema file 'database/schema.sql' not found!</p>";
        }

    } else {
        echo "<p style='color: green;'>✓ Found " . count($tables) . " tables: " . implode(', ', $tables) . "</p>";
    }

    // Check for admin users if admin_users table exists
    if (in_array('admin_users', $tables)) {
        echo "<p><strong>Step 7:</strong> Checking admin users...</p>";
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_users");
        $result = $stmt->fetch();

        if ($result['count'] > 0) {
            echo "<p style='color: green;'>✓ Found {$result['count']} admin user(s). System is ready!</p>";

            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h3>🎉 Success! Your system is ready to use:</h3>";
            echo "<ul>";
            echo "<li><a href='index.html' target='_blank'>📱 Mobile App</a> - For cemetery visitors</li>";
            echo "<li><a href='admin/' target='_blank'>💻 Admin Panel</a> - For cemetery management</li>";
            echo "<li><a href='test.php' target='_blank'>🧪 System Tests</a> - Verify everything works</li>";
            echo "</ul>";
            echo "</div>";

        } else {
            echo "<p style='color: orange;'>⚠ No admin users found.</p>";
            echo "<p><a href='install.php' style='background: #007cba; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>Complete Installation</a> to create an admin user.</p>";
        }
    }

} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Error checking tables: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Database Setup - Sagay Cemetery</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 50px auto; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 { 
            color: #2c3e50; 
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h3 { 
            color: #2c3e50; 
            margin-top: 30px;
        }
        a { 
            color: #3498db; 
            text-decoration: none; 
            font-weight: bold;
        }
        a:hover { 
            text-decoration: underline; 
        }
        ul, ol { 
            margin: 10px 0; 
            padding-left: 30px;
        }
        li { 
            margin: 5px 0; 
        }
        .actions {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="actions">
            <h3>Quick Actions:</h3>
            <a href="setup_database.php" class="btn">Refresh Status</a>
            <a href="install.php" class="btn">Installation Wizard</a>
            <a href="http://localhost/phpmyadmin" class="btn" target="_blank">Open phpMyAdmin</a>
            <a href="test.php" class="btn">Test System</a>
        </div>
    </div>
</body>
</html>
