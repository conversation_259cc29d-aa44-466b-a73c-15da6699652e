<?php
/**
 * Search API Endpoint
 */

function handleSearchRequest($method) {
    if ($method !== 'GET') {
        sendResponse(405, ['error' => 'Method not allowed']);
    }
    
    $query = $_GET['q'] ?? '';
    $type = $_GET['type'] ?? 'name'; // name, grave_number, qr_code
    $section_id = $_GET['section_id'] ?? null;
    $limit = min(intval($_GET['limit'] ?? 20), 50); // Max 50 results
    $offset = intval($_GET['offset'] ?? 0);
    
    // User location for distance calculation
    $user_lat = $_GET['lat'] ?? null;
    $user_lng = $_GET['lng'] ?? null;
    
    if (empty($query)) {
        sendResponse(400, ['error' => 'Search query is required']);
    }
    
    try {
        $db = getDBConnection();
        $results = searchGraves($db, $query, $type, $section_id, $limit, $offset, $user_lat, $user_lng);
        
        // Log the search
        logSearch($query, $type, count($results), $user_lat, $user_lng);
        
        sendResponse(200, [
            'success' => true,
            'query' => $query,
            'type' => $type,
            'results' => $results,
            'total_results' => count($results)
        ]);
        
    } catch (Exception $e) {
        sendResponse(500, ['error' => 'Search failed: ' . $e->getMessage()]);
    }
}

function searchGraves($db, $query, $type, $section_id, $limit, $offset, $user_lat, $user_lng) {
    $where_conditions = ['g.is_active = 1'];
    $params = [];
    
    // Build search condition based on type
    switch ($type) {
        case 'name':
            $where_conditions[] = 'g.deceased_name_search LIKE ?';
            $params[] = '%' . strtolower($query) . '%';
            break;
            
        case 'grave_number':
            $where_conditions[] = 'g.grave_number LIKE ?';
            $params[] = '%' . $query . '%';
            break;
            
        case 'qr_code':
            $where_conditions[] = 'g.qr_code = ?';
            $params[] = $query;
            break;
            
        default:
            // Default to name search
            $where_conditions[] = 'g.deceased_name_search LIKE ?';
            $params[] = '%' . strtolower($query) . '%';
    }
    
    // Add section filter if specified
    if ($section_id) {
        $where_conditions[] = 'g.section_id = ?';
        $params[] = $section_id;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Build the main query
    $sql = "
        SELECT 
            g.id,
            g.grave_number,
            g.deceased_name,
            g.birth_date,
            g.death_date,
            g.burial_date,
            g.latitude,
            g.longitude,
            g.plot_size,
            g.grave_type,
            g.notes,
            g.photo_filename,
            g.qr_code,
            s.section_name,
            s.section_code
        FROM graves g
        JOIN cemetery_sections s ON g.section_id = s.id
        WHERE {$where_clause}
        ORDER BY g.deceased_name
        LIMIT ? OFFSET ?
    ";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $results = $stmt->fetchAll();
    
    // Process results
    $processed_results = [];
    foreach ($results as $row) {
        $grave = [
            'id' => intval($row['id']),
            'grave_number' => $row['grave_number'],
            'deceased_name' => $row['deceased_name'],
            'birth_date' => formatDate($row['birth_date']),
            'death_date' => formatDate($row['death_date']),
            'burial_date' => formatDate($row['burial_date']),
            'coordinates' => [
                'latitude' => floatval($row['latitude']),
                'longitude' => floatval($row['longitude'])
            ],
            'plot_size' => $row['plot_size'],
            'grave_type' => $row['grave_type'],
            'notes' => $row['notes'],
            'section' => [
                'name' => $row['section_name'],
                'code' => $row['section_code']
            ],
            'photo_url' => $row['photo_filename'] ? UPLOAD_URL . $row['photo_filename'] : null,
            'qr_code' => $row['qr_code']
        ];
        
        // Calculate distance if user location is provided
        if ($user_lat && $user_lng && validateGPSCoordinates($user_lat, $user_lng)) {
            $distance = calculateDistance(
                floatval($user_lat), 
                floatval($user_lng),
                floatval($row['latitude']), 
                floatval($row['longitude'])
            );
            $grave['distance_meters'] = round($distance, 2);
        }
        
        $processed_results[] = $grave;
    }
    
    // Sort by distance if user location is provided
    if ($user_lat && $user_lng) {
        usort($processed_results, function($a, $b) {
            $dist_a = $a['distance_meters'] ?? PHP_INT_MAX;
            $dist_b = $b['distance_meters'] ?? PHP_INT_MAX;
            return $dist_a <=> $dist_b;
        });
    }
    
    return $processed_results;
}
?>
