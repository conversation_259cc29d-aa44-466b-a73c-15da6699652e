<?php
/**
 * Admin Panel for Sagay Cemetery Grave Locator
 */

session_start();
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

// Get admin user info
$admin_user = $_SESSION['admin_user'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Sagay Cemetery</title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-cross"></i> Cemetery Admin</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="#dashboard" class="nav-link active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a></li>
                    <li><a href="#graves" class="nav-link" data-page="graves">
                        <i class="fas fa-map-marker-alt"></i> Manage Graves
                    </a></li>
                    <li><a href="#sections" class="nav-link" data-page="sections">
                        <i class="fas fa-th-large"></i> Cemetery Sections
                    </a></li>
                    <li><a href="#analytics" class="nav-link" data-page="analytics">
                        <i class="fas fa-chart-bar"></i> Analytics
                    </a></li>
                    <li><a href="#users" class="nav-link" data-page="users">
                        <i class="fas fa-users"></i> Admin Users
                    </a></li>
                    <li><a href="#settings" class="nav-link" data-page="settings">
                        <i class="fas fa-cog"></i> Settings
                    </a></li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <div class="admin-info">
                    <p><strong><?php echo htmlspecialchars($admin_user['full_name']); ?></strong></p>
                    <p><?php echo htmlspecialchars($admin_user['role']); ?></p>
                </div>
                <a href="logout.php" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="main-header">
                <div class="header-left">
                    <button class="sidebar-toggle" id="sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 id="page-title">Dashboard</h1>
                </div>
                
                <div class="header-right">
                    <div class="header-stats">
                        <span id="current-time"></span>
                    </div>
                    <div class="user-menu">
                        <img src="../assets/images/default-avatar.png" alt="Admin" class="user-avatar">
                        <span><?php echo htmlspecialchars($admin_user['username']); ?></span>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="page-content">
                <!-- Dashboard Page -->
                <div id="dashboard-page" class="admin-page active">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="total-graves">Loading...</h3>
                                <p>Total Graves</p>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="total-searches">Loading...</h3>
                                <p>Total Searches</p>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="searches-today">Loading...</h3>
                                <p>Searches Today</p>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-th-large"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="total-sections">Loading...</h3>
                                <p>Cemetery Sections</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Recent Searches</h3>
                                <a href="#analytics" class="view-all">View All</a>
                            </div>
                            <div class="card-content">
                                <div id="recent-searches">Loading...</div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Popular Graves</h3>
                                <a href="#analytics" class="view-all">View All</a>
                            </div>
                            <div class="card-content">
                                <div id="popular-graves">Loading...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Graves Management Page -->
                <div id="graves-page" class="admin-page">
                    <div class="page-header">
                        <h2>Manage Graves</h2>
                        <button class="btn btn-primary" id="add-grave-btn">
                            <i class="fas fa-plus"></i> Add New Grave
                        </button>
                    </div>
                    
                    <div class="filters-bar">
                        <div class="search-box">
                            <input type="text" id="graves-search" placeholder="Search graves...">
                            <i class="fas fa-search"></i>
                        </div>
                        <select id="section-filter">
                            <option value="">All Sections</option>
                        </select>
                        <select id="type-filter">
                            <option value="">All Types</option>
                            <option value="regular">Regular</option>
                            <option value="family">Family Plot</option>
                            <option value="mausoleum">Mausoleum</option>
                            <option value="columbarium">Columbarium</option>
                        </select>
                    </div>
                    
                    <div class="table-container">
                        <table class="data-table" id="graves-table">
                            <thead>
                                <tr>
                                    <th>Grave #</th>
                                    <th>Deceased Name</th>
                                    <th>Section</th>
                                    <th>Type</th>
                                    <th>Death Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="graves-table-body">
                                <tr>
                                    <td colspan="7" class="loading">Loading graves...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="pagination" id="graves-pagination"></div>
                </div>

                <!-- Other pages will be loaded dynamically -->
                <div id="sections-page" class="admin-page">
                    <h2>Cemetery Sections</h2>
                    <p>Section management functionality will be loaded here.</p>
                </div>

                <div id="analytics-page" class="admin-page">
                    <h2>Analytics & Reports</h2>
                    <p>Analytics functionality will be loaded here.</p>
                </div>

                <div id="users-page" class="admin-page">
                    <h2>Admin Users</h2>
                    <p>User management functionality will be loaded here.</p>
                </div>

                <div id="settings-page" class="admin-page">
                    <h2>System Settings</h2>
                    <p>Settings functionality will be loaded here.</p>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="grave-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="grave-modal-title">Add New Grave</h3>
                <button class="close-btn" id="close-grave-modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="grave-form">
                    <!-- Form content will be populated by JavaScript -->
                </form>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Scripts -->
    <script src="../assets/js/admin.js"></script>
    <script src="../assets/js/admin-graves.js"></script>
</body>
</html>
