<?php
/**
 * Cemetery Sections API Endpoint
 */

function handleSectionsRequest($method, $id) {
    switch ($method) {
        case 'GET':
            if ($id) {
                getSectionById($id);
            } else {
                getAllSections();
            }
            break;
            
        case 'POST':
            if (!isAdmin()) {
                sendResponse(401, ['error' => 'Unauthorized']);
            }
            createSection();
            break;
            
        case 'PUT':
            if (!isAdmin()) {
                sendResponse(401, ['error' => 'Unauthorized']);
            }
            if (!$id) {
                sendResponse(400, ['error' => 'Section ID is required']);
            }
            updateSection($id);
            break;
            
        case 'DELETE':
            if (!isAdmin()) {
                sendResponse(401, ['error' => 'Unauthorized']);
            }
            if (!$id) {
                sendResponse(400, ['error' => 'Section ID is required']);
            }
            deleteSection($id);
            break;
            
        default:
            sendResponse(405, ['error' => 'Method not allowed']);
    }
}

function getAllSections() {
    try {
        $db = getDBConnection();
        
        $stmt = $db->prepare("
            SELECT 
                s.*,
                COUNT(g.id) as grave_count
            FROM cemetery_sections s
            LEFT JOIN graves g ON s.id = g.section_id AND g.is_active = 1
            WHERE s.is_active = 1
            GROUP BY s.id
            ORDER BY s.section_name
        ");
        
        $stmt->execute();
        $sections = $stmt->fetchAll();
        
        $results = [];
        foreach ($sections as $section) {
            $results[] = [
                'id' => intval($section['id']),
                'section_name' => $section['section_name'],
                'section_code' => $section['section_code'],
                'description' => $section['description'],
                'coordinates' => [
                    'latitude' => floatval($section['center_latitude']),
                    'longitude' => floatval($section['center_longitude'])
                ],
                'grave_count' => intval($section['grave_count']),
                'created_at' => $section['created_at']
            ];
        }
        
        sendResponse(200, [
            'success' => true,
            'sections' => $results
        ]);
        
    } catch (Exception $e) {
        sendResponse(500, ['error' => 'Failed to retrieve sections: ' . $e->getMessage()]);
    }
}

function getSectionById($id) {
    try {
        $db = getDBConnection();
        
        $stmt = $db->prepare("
            SELECT 
                s.*,
                COUNT(g.id) as grave_count
            FROM cemetery_sections s
            LEFT JOIN graves g ON s.id = g.section_id AND g.is_active = 1
            WHERE s.id = ? AND s.is_active = 1
            GROUP BY s.id
        ");
        
        $stmt->execute([$id]);
        $section = $stmt->fetch();
        
        if (!$section) {
            sendResponse(404, ['error' => 'Section not found']);
        }
        
        $result = [
            'id' => intval($section['id']),
            'section_name' => $section['section_name'],
            'section_code' => $section['section_code'],
            'description' => $section['description'],
            'coordinates' => [
                'latitude' => floatval($section['center_latitude']),
                'longitude' => floatval($section['center_longitude'])
            ],
            'grave_count' => intval($section['grave_count']),
            'created_at' => $section['created_at']
        ];
        
        sendResponse(200, ['success' => true, 'section' => $result]);
        
    } catch (Exception $e) {
        sendResponse(500, ['error' => 'Failed to retrieve section: ' . $e->getMessage()]);
    }
}

function createSection() {
    $data = getJsonInput();
    
    $required_fields = ['section_name', 'section_code', 'center_latitude', 'center_longitude'];
    $missing_fields = validateRequiredFields($data, $required_fields);
    
    if (!empty($missing_fields)) {
        sendResponse(400, ['error' => 'Missing required fields: ' . implode(', ', $missing_fields)]);
    }
    
    // Validate GPS coordinates
    if (!validateGPSCoordinates($data['center_latitude'], $data['center_longitude'])) {
        sendResponse(400, ['error' => 'Invalid GPS coordinates']);
    }
    
    try {
        $db = getDBConnection();
        
        // Check if section code already exists
        $stmt = $db->prepare("SELECT id FROM cemetery_sections WHERE section_code = ? AND is_active = 1");
        $stmt->execute([$data['section_code']]);
        if ($stmt->fetch()) {
            sendResponse(400, ['error' => 'Section code already exists']);
        }
        
        $stmt = $db->prepare("
            INSERT INTO cemetery_sections 
            (section_name, section_code, description, center_latitude, center_longitude) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            sanitizeString($data['section_name']),
            sanitizeString($data['section_code']),
            sanitizeString($data['description'] ?? ''),
            floatval($data['center_latitude']),
            floatval($data['center_longitude'])
        ]);
        
        $section_id = $db->lastInsertId();
        
        sendResponse(201, [
            'success' => true,
            'message' => 'Section created successfully',
            'section_id' => intval($section_id)
        ]);
        
    } catch (Exception $e) {
        sendResponse(500, ['error' => 'Failed to create section: ' . $e->getMessage()]);
    }
}

function updateSection($id) {
    $data = getJsonInput();
    
    try {
        $db = getDBConnection();
        
        // Check if section exists
        $stmt = $db->prepare("SELECT id FROM cemetery_sections WHERE id = ? AND is_active = 1");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            sendResponse(404, ['error' => 'Section not found']);
        }
        
        // Build update query dynamically
        $update_fields = [];
        $params = [];
        
        $allowed_fields = ['section_name', 'section_code', 'description', 'center_latitude', 'center_longitude'];
        
        foreach ($allowed_fields as $field) {
            if (isset($data[$field])) {
                if (in_array($field, ['center_latitude', 'center_longitude'])) {
                    if (!validateGPSCoordinates($data['center_latitude'] ?? 0, $data['center_longitude'] ?? 0)) {
                        sendResponse(400, ['error' => 'Invalid GPS coordinates']);
                    }
                    $update_fields[] = "{$field} = ?";
                    $params[] = floatval($data[$field]);
                } else {
                    $update_fields[] = "{$field} = ?";
                    $params[] = sanitizeString($data[$field]);
                }
            }
        }
        
        if (empty($update_fields)) {
            sendResponse(400, ['error' => 'No valid fields to update']);
        }
        
        // Check for duplicate section code if updating
        if (isset($data['section_code'])) {
            $stmt = $db->prepare("SELECT id FROM cemetery_sections WHERE section_code = ? AND id != ? AND is_active = 1");
            $stmt->execute([$data['section_code'], $id]);
            if ($stmt->fetch()) {
                sendResponse(400, ['error' => 'Section code already exists']);
            }
        }
        
        $params[] = $id;
        
        $sql = "UPDATE cemetery_sections SET " . implode(', ', $update_fields) . " WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        
        sendResponse(200, [
            'success' => true,
            'message' => 'Section updated successfully'
        ]);
        
    } catch (Exception $e) {
        sendResponse(500, ['error' => 'Failed to update section: ' . $e->getMessage()]);
    }
}

function deleteSection($id) {
    try {
        $db = getDBConnection();
        
        // Check if section has graves
        $stmt = $db->prepare("SELECT COUNT(*) as grave_count FROM graves WHERE section_id = ? AND is_active = 1");
        $stmt->execute([$id]);
        $result = $stmt->fetch();
        
        if ($result['grave_count'] > 0) {
            sendResponse(400, ['error' => 'Cannot delete section with existing graves']);
        }
        
        // Soft delete - set is_active to 0
        $stmt = $db->prepare("UPDATE cemetery_sections SET is_active = 0 WHERE id = ?");
        $stmt->execute([$id]);
        
        if ($stmt->rowCount() === 0) {
            sendResponse(404, ['error' => 'Section not found']);
        }
        
        sendResponse(200, [
            'success' => true,
            'message' => 'Section deleted successfully'
        ]);
        
    } catch (Exception $e) {
        sendResponse(500, ['error' => 'Failed to delete section: ' . $e->getMessage()]);
    }
}
?>
