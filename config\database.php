<?php
/**
 * Database Configuration for Sagay Cemetery Grave Locator
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'sagay_cemetery';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function getConnection() {
        $this->conn = null;

        try {
            // First try to connect to the specific database
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8",
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                )
            );
        } catch(PDOException $exception) {
            // If database doesn't exist, try to create it
            try {
                $temp_conn = new PDO(
                    "mysql:host=" . $this->host,
                    $this->username,
                    $this->password
                );
                $temp_conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                // Create database if it doesn't exist
                $temp_conn->exec("CREATE DATABASE IF NOT EXISTS `" . $this->db_name . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

                // Now connect to the created database
                $this->conn = new PDO(
                    "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                    $this->username,
                    $this->password,
                    array(
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8",
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                    )
                );
            } catch(PDOException $create_exception) {
                die("Database connection failed: " . $create_exception->getMessage() .
                    "<br><br>Please ensure:<br>" .
                    "1. XAMPP MySQL is running<br>" .
                    "2. Database credentials are correct<br>" .
                    "3. MySQL user has permission to create databases<br><br>" .
                    "<a href='install.php'>Run Installation Wizard</a>");
            }
        }

        return $this->conn;
    }
}

// Database connection helper function
function getDBConnection() {
    $database = new Database();
    return $database->getConnection();
}
?>
