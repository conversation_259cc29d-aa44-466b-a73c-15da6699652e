<?php
/**
 * Simple Database Configuration for Sagay Cemetery Grave Locator
 * This version avoids complex error handling that might cause server errors
 */

// Database configuration constants
define('DB_HOST', 'localhost');
define('DB_NAME', 'sagay_cemetery');
define('DB_USER', 'root');
define('DB_PASS', '');

/**
 * Get database connection
 * Returns PDO connection or false on failure
 */
function getDBConnection() {
    static $connection = null;

    if ($connection !== null) {
        return $connection;
    }

    try {
        // Try to connect to the database
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];

        $connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        return $connection;

    } catch (PDOException $e) {
        // If database doesn't exist, try to create it
        try {
            $dsn = "mysql:host=" . DB_HOST . ";charset=utf8mb4";
            $temp_conn = new PDO($dsn, DB_USER, DB_PASS, $options);

            // Create database
            $sql = "CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            $temp_conn->exec($sql);

            // Now connect to the created database
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $connection = new PDO($dsn, DB_USER, DB_PASS, $options);
            return $connection;

        } catch (PDOException $e2) {
            // Return false instead of dying to prevent server errors
            error_log("Database connection failed: " . $e2->getMessage());
            return false;
        }
    }
}

/**
 * Check if database connection is working
 */
function testDatabaseConnection() {
    $conn = getDBConnection();
    if ($conn === false) {
        return false;
    }

    try {
        $conn->query("SELECT 1");
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Legacy Database class for compatibility
 */
class Database {
    public function getConnection() {
        return getDBConnection();
    }
}
?>
