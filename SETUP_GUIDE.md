# Sagay Public Cemetery Grave Locator - Setup Guide

## Quick Start

1. **Run the installer**: Navigate to `http://localhost/ngeks/install.php`
2. **Follow the installation wizard** (6 steps)
3. **Access the system**:
   - Mobile App: `http://localhost/ngeks/`
   - Admin Panel: `http://localhost/ngeks/admin/`

## Detailed Setup Instructions

### Prerequisites

- **XAMPP** (or similar LAMP/WAMP stack)
- **PHP 7.4+** with PDO MySQL extension
- **MySQL 5.7+**
- **Modern web browser** with GPS support
- **Google Maps API Key** (optional but recommended)

### Step 1: Database Setup

1. Start XAMPP and ensure Apache and MySQL are running
2. Open phpMyAdmin (`http://localhost/phpmyadmin`)
3. Create a new database named `sagay_cemetery`
4. Set collation to `utf8mb4_unicode_ci`

### Step 2: File Installation

1. Extract/copy all files to `C:\xampp\htdocs\ngeks\`
2. Ensure proper file permissions (755 for directories, 644 for files)
3. Create `uploads` directory if it doesn't exist

### Step 3: Run Installation Wizard

1. Navigate to `http://localhost/ngeks/install.php`
2. Follow these steps:

   **Step 1: Welcome**
   - Review requirements
   - Click "Start Installation"

   **Step 2: Database Configuration**
   - Host: `localhost`
   - Database: `sagay_cemetery`
   - Username: `root` (default XAMPP)
   - Password: (leave empty for default XAMPP)

   **Step 3: Import Schema**
   - Click "Import Schema" to create tables

   **Step 4: Create Admin User**
   - Username: Choose admin username
   - Email: Your email address
   - Full Name: Your full name
   - Password: Strong password

   **Step 5: Final Configuration**
   - Google Maps API Key: (optional, can be added later)

   **Step 6: Complete**
   - Installation finished!

### Step 4: Google Maps API Setup (Recommended)

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable these APIs:
   - Maps JavaScript API
   - Geocoding API
   - Directions API
4. Create credentials (API Key)
5. Restrict the API key to your domain
6. Add the API key to `config/config.php`:
   ```php
   define('GOOGLE_MAPS_API_KEY', 'your-api-key-here');
   ```

### Step 5: Initial Data Setup

1. Login to admin panel: `http://localhost/ngeks/admin/`
2. Add cemetery sections:
   - Go to "Cemetery Sections"
   - Add sections with GPS coordinates
3. Add graves:
   - Go to "Manage Graves"
   - Add individual graves with details and GPS coordinates

## System Architecture

### Frontend (Mobile App)
- **Progressive Web App (PWA)** for mobile compatibility
- **Responsive design** works on phones, tablets, and desktops
- **Offline capability** for basic searches
- **GPS integration** for navigation

### Backend (API)
- **PHP REST API** with clean endpoints
- **MySQL database** with optimized schema
- **Search functionality** with multiple criteria
- **Admin authentication** and authorization

### Admin Panel
- **Web-based dashboard** for management
- **Grave management** (CRUD operations)
- **Analytics and reporting**
- **User activity monitoring**

## File Structure

```
ngeks/
├── admin/                  # Admin panel
│   ├── index.php          # Main admin dashboard
│   ├── login.php          # Admin login
│   └── logout.php         # Admin logout
├── api/                   # REST API
│   ├── index.php          # API router
│   └── endpoints/         # API endpoint handlers
├── assets/                # Static assets
│   ├── css/              # Stylesheets
│   ├── js/               # JavaScript files
│   └── images/           # Images and icons
├── config/                # Configuration files
│   ├── config.php        # Main configuration
│   └── database.php      # Database connection
├── database/              # Database files
│   └── schema.sql        # Database schema
├── includes/              # PHP includes
│   └── functions.php     # Common functions
├── uploads/               # Uploaded files
├── index.html            # Mobile app entry point
├── manifest.json         # PWA manifest
├── sw.js                 # Service worker
├── install.php           # Installation wizard
└── README.md             # Documentation
```

## API Endpoints

### Public Endpoints
- `GET /api/search` - Search graves
- `GET /api/graves/{id}` - Get grave details
- `GET /api/sections` - Get cemetery sections

### Admin Endpoints (Require Authentication)
- `POST /api/graves` - Create grave
- `PUT /api/graves/{id}` - Update grave
- `DELETE /api/graves/{id}` - Delete grave
- `GET /api/stats?detailed=true` - Get detailed statistics

## Configuration Options

### Main Configuration (`config/config.php`)
- Site settings (name, URL, email)
- Google Maps API key
- File upload settings
- Security settings
- Debug mode

### Database Configuration (`config/database.php`)
- Database connection settings
- Generated automatically by installer

## Security Features

- **Password hashing** using PHP's password_hash()
- **SQL injection protection** using prepared statements
- **XSS protection** with input sanitization
- **CSRF protection** for admin forms
- **Session management** with timeouts
- **Input validation** on all forms

## Mobile Features

- **GPS location** detection and usage
- **Offline search** capability
- **Touch-friendly** interface
- **PWA installation** on mobile devices
- **QR code scanning** for quick grave access
- **Turn-by-turn navigation** integration

## Admin Features

- **Dashboard** with statistics
- **Grave management** (add, edit, delete)
- **Section management** for cemetery organization
- **Search analytics** and reporting
- **User activity monitoring**
- **Bulk operations** for data management

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check MySQL is running in XAMPP
   - Verify database credentials
   - Ensure database exists

2. **Maps Not Loading**
   - Check Google Maps API key
   - Verify API key restrictions
   - Check browser console for errors

3. **GPS Not Working**
   - Ensure HTTPS (required for GPS)
   - Check browser permissions
   - Test on mobile device

4. **Search Not Working**
   - Check database has grave data
   - Verify API endpoints are accessible
   - Check browser network tab for errors

### Debug Mode

Enable debug mode in `config/config.php`:
```php
define('DEBUG_MODE', true);
```

This will show detailed error messages and help identify issues.

## Performance Optimization

1. **Database Indexing**
   - Indexes on search fields are included in schema
   - Consider additional indexes for large datasets

2. **Caching**
   - Service worker caches static files
   - API responses cached for offline use

3. **Image Optimization**
   - Compress grave photos before upload
   - Use appropriate image formats (WebP, JPEG)

4. **CDN Usage**
   - Consider using CDN for static assets
   - Google Maps loads from Google's CDN

## Backup and Maintenance

### Regular Backups
1. **Database backup**: Export from phpMyAdmin
2. **File backup**: Copy entire ngeks folder
3. **Configuration backup**: Save config files separately

### Maintenance Tasks
1. **Clean old search logs** periodically
2. **Update grave information** as needed
3. **Monitor disk space** for uploads
4. **Check for PHP/MySQL updates**

## Support and Updates

For support or updates:
1. Check the README.md file
2. Review error logs in XAMPP
3. Test with different browsers/devices
4. Verify all requirements are met

## License

This system is provided under the MIT License. See LICENSE file for details.
