<?php
/**
 * Statistics API Endpoint
 */

function handleStatsRequest($method) {
    if ($method !== 'GET') {
        sendResponse(405, ['error' => 'Method not allowed']);
    }
    
    // Basic stats don't require admin auth, but detailed stats do
    $detailed = isset($_GET['detailed']) && $_GET['detailed'] === 'true';
    
    if ($detailed && !isAdmin()) {
        sendResponse(401, ['error' => 'Unauthorized']);
    }
    
    try {
        $db = getDBConnection();
        $stats = [];
        
        // Basic statistics
        $stats['basic'] = getBasicStats($db);
        
        if ($detailed) {
            $stats['detailed'] = getDetailedStats($db);
        }
        
        sendResponse(200, [
            'success' => true,
            'stats' => $stats
        ]);
        
    } catch (Exception $e) {
        sendResponse(500, ['error' => 'Failed to retrieve statistics: ' . $e->getMessage()]);
    }
}

function getBasicStats($db) {
    $stats = [];
    
    // Total graves
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM graves WHERE is_active = 1");
    $stmt->execute();
    $stats['total_graves'] = intval($stmt->fetch()['total']);
    
    // Total sections
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM cemetery_sections WHERE is_active = 1");
    $stmt->execute();
    $stats['total_sections'] = intval($stmt->fetch()['total']);
    
    // Total searches
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM search_logs");
    $stmt->execute();
    $stats['total_searches'] = intval($stmt->fetch()['total']);
    
    // Searches today
    $stmt = $db->prepare("
        SELECT COUNT(*) as total 
        FROM search_logs 
        WHERE DATE(search_timestamp) = CURDATE()
    ");
    $stmt->execute();
    $stats['searches_today'] = intval($stmt->fetch()['total']);
    
    return $stats;
}

function getDetailedStats($db) {
    $stats = [];
    
    // Grave statistics by type
    $stmt = $db->prepare("
        SELECT grave_type, COUNT(*) as count 
        FROM graves 
        WHERE is_active = 1 
        GROUP BY grave_type
    ");
    $stmt->execute();
    $stats['graves_by_type'] = $stmt->fetchAll();
    
    // Grave statistics by status
    $stmt = $db->prepare("
        SELECT status, COUNT(*) as count 
        FROM graves 
        WHERE is_active = 1 
        GROUP BY status
    ");
    $stmt->execute();
    $stats['graves_by_status'] = $stmt->fetchAll();
    
    // Section statistics
    $stmt = $db->prepare("
        SELECT 
            s.section_name,
            s.section_code,
            COUNT(g.id) as grave_count
        FROM cemetery_sections s
        LEFT JOIN graves g ON s.id = g.section_id AND g.is_active = 1
        WHERE s.is_active = 1
        GROUP BY s.id
        ORDER BY grave_count DESC
    ");
    $stmt->execute();
    $stats['sections'] = $stmt->fetchAll();
    
    // Search statistics by type
    $stmt = $db->prepare("
        SELECT search_type, COUNT(*) as count 
        FROM search_logs 
        GROUP BY search_type
    ");
    $stmt->execute();
    $stats['searches_by_type'] = $stmt->fetchAll();
    
    // Device statistics
    $stmt = $db->prepare("
        SELECT device_type, COUNT(*) as count 
        FROM search_logs 
        GROUP BY device_type
    ");
    $stmt->execute();
    $stats['searches_by_device'] = $stmt->fetchAll();
    
    // Recent searches (last 10)
    $stmt = $db->prepare("
        SELECT 
            search_query,
            search_type,
            results_found,
            device_type,
            search_timestamp
        FROM search_logs 
        ORDER BY search_timestamp DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $stats['recent_searches'] = $stmt->fetchAll();
    
    // Popular graves (most searched)
    $stmt = $db->prepare("
        SELECT 
            g.deceased_name,
            g.grave_number,
            s.section_name,
            COUNT(sl.id) as search_count
        FROM search_logs sl
        JOIN graves g ON LOWER(g.deceased_name) LIKE CONCAT('%', LOWER(sl.search_query), '%')
        JOIN cemetery_sections s ON g.section_id = s.id
        WHERE sl.search_type = 'name' AND sl.results_found > 0
        GROUP BY g.id
        ORDER BY search_count DESC
        LIMIT 10
    ");
    $stmt->execute();
    $stats['popular_graves'] = $stmt->fetchAll();
    
    // Daily search trends (last 30 days)
    $stmt = $db->prepare("
        SELECT 
            DATE(search_timestamp) as search_date,
            COUNT(*) as search_count
        FROM search_logs 
        WHERE search_timestamp >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY DATE(search_timestamp)
        ORDER BY search_date DESC
    ");
    $stmt->execute();
    $stats['daily_trends'] = $stmt->fetchAll();
    
    // Monthly statistics
    $stmt = $db->prepare("
        SELECT 
            YEAR(search_timestamp) as year,
            MONTH(search_timestamp) as month,
            COUNT(*) as search_count
        FROM search_logs 
        WHERE search_timestamp >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
        GROUP BY YEAR(search_timestamp), MONTH(search_timestamp)
        ORDER BY year DESC, month DESC
    ");
    $stmt->execute();
    $stats['monthly_trends'] = $stmt->fetchAll();
    
    // Search success rate
    $stmt = $db->prepare("
        SELECT 
            COUNT(*) as total_searches,
            SUM(CASE WHEN results_found > 0 THEN 1 ELSE 0 END) as successful_searches,
            ROUND((SUM(CASE WHEN results_found > 0 THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as success_rate
        FROM search_logs
    ");
    $stmt->execute();
    $stats['search_success'] = $stmt->fetch();
    
    return $stats;
}
?>
