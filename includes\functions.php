<?php
/**
 * Common Functions for Sagay Cemetery System
 */

/**
 * Send JSON response and exit
 */
function sendResponse($status_code, $data) {
    http_response_code($status_code);
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit();
}

/**
 * Get JSON input from request body
 */
function getJsonInput() {
    $input = file_get_contents('php://input');
    return json_decode($input, true);
}

/**
 * Validate required fields in data array
 */
function validateRequiredFields($data, $required_fields) {
    $missing = [];
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $missing[] = $field;
        }
    }
    return $missing;
}

/**
 * Sanitize string input
 */
function sanitizeString($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

/**
 * Generate QR code string for grave
 */
function generateGraveQRCode($grave_id) {
    return 'GRAVE_' . str_pad($grave_id, 6, '0', STR_PAD_LEFT) . '_' . time();
}

/**
 * Calculate distance between two GPS coordinates (in meters)
 */
function calculateDistance($lat1, $lon1, $lat2, $lon2) {
    $earth_radius = 6371000; // Earth radius in meters
    
    $lat1_rad = deg2rad($lat1);
    $lon1_rad = deg2rad($lon1);
    $lat2_rad = deg2rad($lat2);
    $lon2_rad = deg2rad($lon2);
    
    $delta_lat = $lat2_rad - $lat1_rad;
    $delta_lon = $lon2_rad - $lon1_rad;
    
    $a = sin($delta_lat / 2) * sin($delta_lat / 2) +
         cos($lat1_rad) * cos($lat2_rad) *
         sin($delta_lon / 2) * sin($delta_lon / 2);
    
    $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
    
    return $earth_radius * $c;
}

/**
 * Log search activity
 */
function logSearch($query, $type, $results_count, $user_lat = null, $user_lng = null) {
    try {
        $db = getDBConnection();
        
        $device_type = detectDeviceType();
        $user_ip = $_SERVER['REMOTE_ADDR'] ?? null;
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        $stmt = $db->prepare("
            INSERT INTO search_logs 
            (search_query, search_type, results_found, user_ip, user_agent, device_type, location_latitude, location_longitude) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $query, $type, $results_count, $user_ip, $user_agent, $device_type, $user_lat, $user_lng
        ]);
    } catch (Exception $e) {
        // Log error but don't break the main functionality
        error_log("Search logging failed: " . $e->getMessage());
    }
}

/**
 * Detect device type from user agent
 */
function detectDeviceType() {
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    if (preg_match('/tablet|ipad/i', $user_agent)) {
        return 'tablet';
    } elseif (preg_match('/mobile|android|iphone/i', $user_agent)) {
        return 'mobile';
    } else {
        return 'desktop';
    }
}

/**
 * Validate GPS coordinates
 */
function validateGPSCoordinates($lat, $lng) {
    return (
        is_numeric($lat) && is_numeric($lng) &&
        $lat >= -90 && $lat <= 90 &&
        $lng >= -180 && $lng <= 180
    );
}

/**
 * Format date for display
 */
function formatDate($date, $format = 'M j, Y') {
    if (!$date) return null;
    return date($format, strtotime($date));
}

/**
 * Check if user is admin (basic auth check)
 */
function isAdmin() {
    // This is a basic implementation - in production, use proper JWT or session management
    $headers = getallheaders();
    $auth_header = $headers['Authorization'] ?? '';
    
    if (strpos($auth_header, 'Bearer ') === 0) {
        $token = substr($auth_header, 7);
        // Validate token here
        return validateAdminToken($token);
    }
    
    return false;
}

/**
 * Validate admin token (placeholder - implement proper JWT validation)
 */
function validateAdminToken($token) {
    // This is a placeholder - implement proper JWT validation in production
    return $token === 'admin_token_placeholder';
}

/**
 * Upload and process grave photo
 */
function uploadGravePhoto($file, $grave_id) {
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        throw new Exception('No file uploaded');
    }
    
    // Validate file type
    $file_info = pathinfo($file['name']);
    $extension = strtolower($file_info['extension']);
    
    if (!in_array($extension, ALLOWED_IMAGE_TYPES)) {
        throw new Exception('Invalid file type. Allowed: ' . implode(', ', ALLOWED_IMAGE_TYPES));
    }
    
    // Validate file size
    if ($file['size'] > MAX_FILE_SIZE) {
        throw new Exception('File too large. Maximum size: ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB');
    }
    
    // Generate unique filename
    $filename = 'grave_' . $grave_id . '_' . time() . '.' . $extension;
    $upload_path = UPLOAD_PATH . $filename;
    
    // Create upload directory if it doesn't exist
    if (!is_dir(UPLOAD_PATH)) {
        mkdir(UPLOAD_PATH, 0755, true);
    }
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
        throw new Exception('Failed to upload file');
    }
    
    return $filename;
}
?>
