<?php
/**
 * Simple PHP Information Page
 * Use this to check if P<PERSON> is working and see configuration
 */

echo "<h1>PHP Information Test</h1>";
echo "<p>If you can see this, P<PERSON> is working!</p>";

echo "<h2>Basic Information</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

echo "<h2>Required Extensions</h2>";
$extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
foreach ($extensions as $ext) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? '✓ Loaded' : '✗ Missing';
    $color = $loaded ? 'green' : 'red';
    echo "<p style='color: $color;'><strong>$ext:</strong> $status</p>";
}

echo "<h2>MySQL Connection Test</h2>";
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "<p style='color: green;'>✓ MySQL connection successful!</p>";
    
    $version = $pdo->query('SELECT VERSION()')->fetchColumn();
    echo "<p><strong>MySQL Version:</strong> $version</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ MySQL connection failed: " . $e->getMessage() . "</p>";
}

echo "<h2>File System Test</h2>";
$testFile = 'test_write.txt';
if (file_put_contents($testFile, 'test')) {
    echo "<p style='color: green;'>✓ File write test successful</p>";
    unlink($testFile);
} else {
    echo "<p style='color: red;'>✗ File write test failed</p>";
}

echo "<h2>Quick Actions</h2>";
echo "<p><a href='setup_database.php'>Setup Database</a></p>";
echo "<p><a href='install.php'>Installation Wizard</a></p>";
echo "<p><a href='debug.php'>Debug Information</a></p>";

// Show full phpinfo if requested
if (isset($_GET['full'])) {
    echo "<hr>";
    echo "<h2>Full PHP Information</h2>";
    phpinfo();
} else {
    echo "<p><a href='phpinfo.php?full=1'>Show Full PHP Info</a></p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>PHP Info - Sagay Cemetery</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 20px auto; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2 { 
            color: #2c3e50; 
        }
        a { 
            color: #3498db; 
            text-decoration: none; 
        }
        a:hover { 
            text-decoration: underline; 
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Content is generated by PHP above -->
    </div>
</body>
</html>
