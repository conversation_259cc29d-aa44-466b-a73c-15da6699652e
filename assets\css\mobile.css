/* Sagay Cemetery Mobile App Styles */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --light-gray: #ecf0f1;
    --dark-gray: #7f8c8d;
    --white: #ffffff;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: var(--light-gray);
    color: var(--primary-color);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    color: var(--white);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content h1 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.2rem;
}

.header-content p {
    font-size: 0.9rem;
    opacity: 0.9;
}

.menu-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.menu-btn span {
    width: 25px;
    height: 3px;
    background: var(--white);
    border-radius: 2px;
    transition: var(--transition);
}

.menu-btn.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.menu-btn.active span:nth-child(2) {
    opacity: 0;
}

.menu-btn.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Navigation */
.nav-menu {
    background: var(--white);
    box-shadow: var(--shadow);
    transform: translateY(-100%);
    transition: var(--transition);
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 99;
}

.nav-menu.active {
    transform: translateY(0);
}

.nav-menu ul {
    list-style: none;
    padding: 1rem 0;
}

.nav-menu li {
    border-bottom: 1px solid var(--light-gray);
}

.nav-menu li:last-child {
    border-bottom: none;
}

.nav-link {
    display: block;
    padding: 1rem 1.5rem;
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

.nav-link:hover,
.nav-link.active {
    background: var(--light-gray);
    color: var(--secondary-color);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 1rem;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

/* Search Page */
.search-container {
    margin-bottom: 2rem;
}

.search-box {
    position: relative;
    margin-bottom: 1rem;
}

.search-box input {
    width: 100%;
    padding: 1rem 3rem 1rem 1rem;
    border: 2px solid var(--light-gray);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-btn {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: var(--secondary-color);
    color: var(--white);
    border: none;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background: #2980b9;
}

.search-filters select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--light-gray);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background: var(--white);
}

/* Search Results */
.search-results {
    display: grid;
    gap: 1rem;
}

.grave-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    cursor: pointer;
    transition: var(--transition);
}

.grave-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.grave-card h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.grave-info {
    display: grid;
    gap: 0.3rem;
    margin-bottom: 1rem;
}

.grave-info span {
    font-size: 0.9rem;
    color: var(--dark-gray);
}

.grave-actions {
    display: flex;
    gap: 0.5rem;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    transition: var(--transition);
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-secondary {
    background: var(--light-gray);
    color: var(--primary-color);
}

.btn-secondary:hover {
    background: var(--dark-gray);
    color: var(--white);
}

/* No Results */
.no-results {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--dark-gray);
}

.no-results-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* Map Page */
.map-controls {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.control-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--white);
    border: 2px solid var(--light-gray);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.control-btn:hover {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

.map-container {
    height: 70vh;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

/* QR Scanner */
.qr-scanner-container {
    text-align: center;
}

.qr-scanner-container h2 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.qr-scanner-container p {
    margin-bottom: 2rem;
    color: var(--dark-gray);
}

.qr-scanner {
    position: relative;
    width: 100%;
    max-width: 400px;
    margin: 0 auto 2rem;
    border-radius: var(--border-radius);
    overflow: hidden;
    background: #000;
}

#qr-video {
    width: 100%;
    height: auto;
    display: block;
}

.qr-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.qr-frame {
    width: 200px;
    height: 200px;
    border: 3px solid var(--secondary-color);
    border-radius: var(--border-radius);
    position: relative;
}

.qr-frame::before,
.qr-frame::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 30px;
    border: 3px solid var(--white);
}

.qr-frame::before {
    top: -3px;
    left: -3px;
    border-right: none;
    border-bottom: none;
}

.qr-frame::after {
    bottom: -3px;
    right: -3px;
    border-left: none;
    border-top: none;
}

.qr-controls {
    margin-bottom: 2rem;
}

.qr-manual-input {
    background: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.qr-manual-input input {
    width: 100%;
    padding: 0.75rem;
    margin: 1rem 0;
    border: 2px solid var(--light-gray);
    border-radius: var(--border-radius);
    font-size: 1rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    padding: 1rem;
}

.modal.active {
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--light-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    color: var(--primary-color);
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--dark-gray);
    padding: 0.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--light-gray);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1001;
}

.toast {
    background: var(--primary-color);
    color: var(--white);
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow);
    transform: translateX(100%);
    transition: var(--transition);
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background: var(--success-color);
}

.toast.error {
    background: var(--accent-color);
}

.toast.warning {
    background: var(--warning-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding: 0.5rem;
    }
    
    .grave-actions {
        flex-direction: column;
    }
    
    .modal-footer {
        flex-direction: column;
    }
    
    .map-controls {
        justify-content: center;
    }
    
    .control-btn {
        flex: 1;
        min-width: 120px;
    }
}

@media (max-width: 480px) {
    .header-content h1 {
        font-size: 1rem;
    }
    
    .header-content p {
        font-size: 0.8rem;
    }
    
    .search-box input {
        padding: 0.75rem 2.5rem 0.75rem 0.75rem;
    }
    
    .search-btn {
        padding: 0.5rem;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
