/* Ad<PERSON>gin Styles */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --danger-color: #e74c3c;
    --white: #ffffff;
    --light-gray: #ecf0f1;
    --dark-gray: #7f8c8d;
    --shadow: 0 10px 30px rgba(0,0,0,0.2);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="cross" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M10,0 L10,20 M0,10 L20,10" stroke="rgba(255,255,255,0.1)" stroke-width="1" fill="none"/></pattern></defs><rect width="100" height="100" fill="url(%23cross)"/></svg>') repeat;
    opacity: 0.3;
    z-index: 1;
}

.login-container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 400px;
    padding: 2rem;
}

.login-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    background: linear-gradient(135deg, var(--primary-color), #34495e);
    color: var(--white);
    padding: 2rem;
    text-align: center;
}

.logo {
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
}

.login-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.login-header p {
    opacity: 0.9;
    font-size: 0.9rem;
}

.error-message {
    background: #fdf2f2;
    color: var(--danger-color);
    padding: 1rem;
    margin: 1rem;
    border-radius: 8px;
    border: 1px solid #fecaca;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.login-form {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    left: 1rem;
    color: var(--dark-gray);
    z-index: 1;
}

.input-group input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid var(--light-gray);
    border-radius: 8px;
    font-size: 1rem;
    transition: var(--transition);
    background: var(--white);
}

.input-group input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.toggle-password {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: var(--dark-gray);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: var(--transition);
}

.toggle-password:hover {
    color: var(--secondary-color);
    background: rgba(52, 152, 219, 0.1);
}

.login-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, var(--secondary-color), #2980b9);
    color: var(--white);
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.login-btn:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.login-footer {
    background: #f8f9fa;
    padding: 1.5rem;
    text-align: center;
    color: var(--dark-gray);
    font-size: 0.8rem;
    border-top: 1px solid var(--light-gray);
}

.login-footer p {
    margin-bottom: 0.25rem;
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container {
        padding: 1rem;
    }
    
    .login-form {
        padding: 1.5rem;
    }
    
    .login-header {
        padding: 1.5rem;
    }
    
    .login-header h1 {
        font-size: 1.3rem;
    }
    
    .logo {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}

/* Loading Animation */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Focus styles for accessibility */
.input-group input:focus,
.login-btn:focus,
.toggle-password:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .login-card {
        border: 2px solid var(--primary-color);
    }
    
    .input-group input {
        border-width: 3px;
    }
    
    .login-btn {
        border: 2px solid var(--primary-color);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .login-card {
        animation: none;
    }
    
    .login-btn:hover {
        transform: none;
    }
    
    * {
        transition: none;
    }
}
