<?php
/**
 * Main Configuration File for Sagay Cemetery System
 */

// Site Configuration
define('SITE_NAME', 'Sagay Public Cemetery Grave Locator');
define('SITE_URL', 'http://localhost/ngeks');
define('ADMIN_EMAIL', '<EMAIL>');

// API Configuration
define('API_VERSION', 'v1');
define('API_BASE_URL', SITE_URL . '/api');

// Google Maps API Configuration
define('GOOGLE_MAPS_API_KEY', 'YOUR_GOOGLE_MAPS_API_KEY_HERE');

// Cemetery Location (Sagay City, Negros Occidental, Philippines)
define('CEMETERY_CENTER_LAT', 10.8967);
define('CEMETERY_CENTER_LNG', 123.4167);
define('CEMETERY_ZOOM_LEVEL', 18);

// File Upload Configuration
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('UPLOAD_URL', SITE_URL . '/uploads/');

// Security Configuration
define('JWT_SECRET_KEY', 'your-secret-key-change-this-in-production');
define('SESSION_TIMEOUT', 3600); // 1 hour

// Pagination
define('RECORDS_PER_PAGE', 20);

// Error Reporting (set to false in production)
define('DEBUG_MODE', true);

if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Timezone
date_default_timezone_set('Asia/Manila');

// CORS Headers for API
function setCORSHeaders() {
    header("Access-Control-Allow-Origin: *");
    header("Content-Type: application/json; charset=UTF-8");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Max-Age: 3600");
    header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");
}

// Helper function to generate secure random strings
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}
?>
