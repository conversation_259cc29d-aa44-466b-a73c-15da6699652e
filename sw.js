/**
 * Service Worker for Sagay Cemetery Grave Locator
 * Provides offline functionality and caching
 */

const CACHE_NAME = 'cemetery-locator-v1.0.0';
const STATIC_CACHE = 'cemetery-static-v1.0.0';
const DYNAMIC_CACHE = 'cemetery-dynamic-v1.0.0';

// Files to cache for offline use
const STATIC_FILES = [
    '/ngeks/',
    '/ngeks/index.html',
    '/ngeks/assets/css/mobile.css',
    '/ngeks/assets/js/app.js',
    '/ngeks/assets/js/search.js',
    '/ngeks/manifest.json',
    // Add more static assets as needed
];

// API endpoints to cache
const CACHEABLE_APIS = [
    '/ngeks/api/search',
    '/ngeks/api/graves',
    '/ngeks/api/sections'
];

// Install event - cache static files
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            })
            .then(() => {
                console.log('Service Worker: Static files cached');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Error caching static files', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Handle API requests
    if (url.pathname.startsWith('/ngeks/api/')) {
        event.respondWith(handleAPIRequest(request));
        return;
    }
    
    // Handle static files
    if (request.method === 'GET') {
        event.respondWith(handleStaticRequest(request));
        return;
    }
});

// Handle API requests with cache-first strategy for search results
async function handleAPIRequest(request) {
    const url = new URL(request.url);
    
    try {
        // For search requests, try cache first for offline support
        if (url.pathname.includes('/search')) {
            const cachedResponse = await caches.match(request);
            
            if (cachedResponse) {
                // Return cached response and update cache in background
                updateCacheInBackground(request);
                return cachedResponse;
            }
        }
        
        // Try network first
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache successful API responses
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        console.log('Service Worker: Network failed, trying cache', error);
        
        // If network fails, try cache
        const cachedResponse = await caches.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline response for API requests
        return new Response(
            JSON.stringify({
                error: 'Offline - No cached data available',
                offline: true
            }),
            {
                status: 503,
                statusText: 'Service Unavailable',
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );
    }
}

// Handle static file requests with cache-first strategy
async function handleStaticRequest(request) {
    try {
        // Try cache first
        const cachedResponse = await caches.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // If not in cache, try network
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache the response
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        console.log('Service Worker: Failed to fetch', request.url, error);
        
        // Return offline page for navigation requests
        if (request.mode === 'navigate') {
            const offlineResponse = await caches.match('/ngeks/index.html');
            return offlineResponse || new Response('Offline', { status: 503 });
        }
        
        throw error;
    }
}

// Update cache in background
async function updateCacheInBackground(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
    } catch (error) {
        console.log('Service Worker: Background cache update failed', error);
    }
}

// Handle background sync for offline actions
self.addEventListener('sync', event => {
    console.log('Service Worker: Background sync', event.tag);
    
    if (event.tag === 'search-sync') {
        event.waitUntil(syncSearchData());
    }
});

// Sync search data when back online
async function syncSearchData() {
    try {
        // Get pending search requests from IndexedDB or localStorage
        // This would sync any offline search requests when back online
        console.log('Service Worker: Syncing search data...');
        
        // Implementation would depend on how you store offline data
        // For now, just log that sync is happening
        
    } catch (error) {
        console.error('Service Worker: Sync failed', error);
    }
}

// Handle push notifications (for future use)
self.addEventListener('push', event => {
    console.log('Service Worker: Push received');
    
    const options = {
        body: event.data ? event.data.text() : 'New notification',
        icon: '/ngeks/assets/images/icon-192x192.png',
        badge: '/ngeks/assets/images/badge-72x72.png',
        vibrate: [200, 100, 200],
        data: {
            url: '/ngeks/'
        },
        actions: [
            {
                action: 'open',
                title: 'Open App',
                icon: '/ngeks/assets/images/open-icon.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/ngeks/assets/images/close-icon.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('Cemetery Locator', options)
    );
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
    console.log('Service Worker: Notification clicked');
    
    event.notification.close();
    
    if (event.action === 'open' || !event.action) {
        event.waitUntil(
            clients.openWindow('/ngeks/')
        );
    }
});

// Log service worker messages
self.addEventListener('message', event => {
    console.log('Service Worker: Message received', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});
