<?php
/**
 * Debug Information for Sagay Cemetery System
 */

echo "<h2>System Debug Information</h2>";

// PHP Information
echo "<h3>PHP Environment</h3>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server Software:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";

// Check PHP Extensions
echo "<h3>PHP Extensions</h3>";
$required_extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'curl'];
foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? '✓' : '✗';
    $color = extension_loaded($ext) ? 'green' : 'red';
    echo "<p style='color: $color;'>$status $ext</p>";
}

// File Permissions
echo "<h3>File Permissions</h3>";
$files_to_check = [
    'config/database.php',
    'config/config.php',
    'database/schema.sql',
    'uploads'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = substr(sprintf('%o', fileperms($file)), -4);
        $readable = is_readable($file) ? '✓' : '✗';
        $writable = is_writable($file) ? '✓' : '✗';
        echo "<p><strong>$file:</strong> Permissions: $perms, Readable: $readable, Writable: $writable</p>";
    } else {
        echo "<p style='color: red;'><strong>$file:</strong> File not found</p>";
    }
}

// Database Connection Test
echo "<h3>Database Connection Test</h3>";
try {
    // Test basic MySQL connection
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "<p style='color: green;'>✓ MySQL server connection successful</p>";
    
    // Test specific database
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=sagay_cemetery", "root", "");
        echo "<p style='color: green;'>✓ Database 'sagay_cemetery' connection successful</p>";
        
        // Check tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p><strong>Tables found:</strong> " . (empty($tables) ? 'None' : implode(', ', $tables)) . "</p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: orange;'>⚠ Database 'sagay_cemetery' not found: " . $e->getMessage() . "</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ MySQL connection failed: " . $e->getMessage() . "</p>";
}

// Configuration Check
echo "<h3>Configuration Check</h3>";
if (file_exists('config/config.php')) {
    include_once 'config/config.php';
    echo "<p style='color: green;'>✓ config.php loaded</p>";
    
    $constants_to_check = ['SITE_NAME', 'SITE_URL', 'GOOGLE_MAPS_API_KEY', 'DEBUG_MODE'];
    foreach ($constants_to_check as $const) {
        if (defined($const)) {
            $value = constant($const);
            if ($const === 'GOOGLE_MAPS_API_KEY' && $value === 'YOUR_GOOGLE_MAPS_API_KEY_HERE') {
                echo "<p style='color: orange;'>⚠ $const: Not configured (using placeholder)</p>";
            } else {
                echo "<p style='color: green;'>✓ $const: " . (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value) . "</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ $const: Not defined</p>";
        }
    }
} else {
    echo "<p style='color: red;'>✗ config/config.php not found</p>";
}

// Apache Modules (if available)
echo "<h3>Apache Modules</h3>";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    $important_modules = ['mod_rewrite', 'mod_headers', 'mod_deflate', 'mod_expires'];
    foreach ($important_modules as $module) {
        $status = in_array($module, $modules) ? '✓' : '✗';
        $color = in_array($module, $modules) ? 'green' : 'red';
        echo "<p style='color: $color;'>$status $module</p>";
    }
} else {
    echo "<p>Apache module information not available</p>";
}

// Error Log Check
echo "<h3>Recent Errors</h3>";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    $errors = file_get_contents($error_log);
    $recent_errors = array_slice(explode("\n", $errors), -10);
    echo "<p><strong>Last 10 lines from error log:</strong></p>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; font-size: 12px;'>";
    echo htmlspecialchars(implode("\n", $recent_errors));
    echo "</pre>";
} else {
    echo "<p>Error log not found or not accessible</p>";
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Debug Info - Sagay Cemetery</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 1000px; 
            margin: 20px auto; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 { 
            color: #2c3e50; 
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h3 { 
            color: #2c3e50; 
            margin-top: 30px;
            border-bottom: 1px solid #ecf0f1;
            padding-bottom: 5px;
        }
        pre {
            overflow-x: auto;
            max-height: 200px;
            overflow-y: auto;
        }
        .actions {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="actions">
            <h3>Quick Actions:</h3>
            <a href="setup_database.php" class="btn">Setup Database</a>
            <a href="install.php" class="btn">Installation Wizard</a>
            <a href="test.php" class="btn">Test System</a>
            <a href="debug.php" class="btn">Refresh Debug Info</a>
        </div>
    </div>
</body>
</html>
