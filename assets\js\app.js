/**
 * Main App JavaScript for Sagay Cemetery Locator
 */

class CemeteryApp {
    constructor() {
        this.currentPage = 'search';
        this.userLocation = null;
        this.map = null;
        this.graves = [];
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.hideLoadingScreen();
        this.requestLocationPermission();
    }
    
    bindEvents() {
        // Menu toggle
        const menuBtn = document.getElementById('menu-btn');
        const navMenu = document.getElementById('nav-menu');
        
        menuBtn.addEventListener('click', () => {
            menuBtn.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
        
        // Navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.dataset.page;
                this.navigateToPage(page);
                
                // Close menu on mobile
                menuBtn.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
        
        // Modal controls
        const modal = document.getElementById('grave-modal');
        const closeModalBtns = document.querySelectorAll('#close-modal, #close-modal-btn');
        
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                this.closeModal();
            });
        });
        
        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal();
            }
        });
        
        // Navigate button in modal
        document.getElementById('navigate-btn').addEventListener('click', () => {
            const graveData = JSON.parse(modal.dataset.graveData || '{}');
            if (graveData.coordinates) {
                this.navigateToGrave(graveData.coordinates.latitude, graveData.coordinates.longitude);
            }
        });
    }
    
    hideLoadingScreen() {
        setTimeout(() => {
            document.getElementById('loading-screen').style.display = 'none';
            document.getElementById('app').style.display = 'flex';
        }, 1500);
    }
    
    navigateToPage(page) {
        // Hide all pages
        document.querySelectorAll('.page').forEach(p => {
            p.classList.remove('active');
        });
        
        // Show selected page
        document.getElementById(`${page}-page`).classList.add('active');
        
        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-page="${page}"]`).classList.add('active');
        
        this.currentPage = page;
        
        // Initialize page-specific functionality
        if (page === 'map' && !this.map) {
            this.initializeMap();
        }
    }
    
    async requestLocationPermission() {
        if ('geolocation' in navigator) {
            try {
                const position = await new Promise((resolve, reject) => {
                    navigator.geolocation.getCurrentPosition(resolve, reject, {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 300000 // 5 minutes
                    });
                });
                
                this.userLocation = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude
                };
                
                this.showToast('Location access granted', 'success');
            } catch (error) {
                console.warn('Location access denied:', error);
                this.showToast('Location access denied. Some features may be limited.', 'warning');
            }
        } else {
            this.showToast('Geolocation not supported by this browser', 'warning');
        }
    }
    
    showToast(message, type = 'info', duration = 3000) {
        const container = document.getElementById('toast-container');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        
        container.appendChild(toast);
        
        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 100);
        
        // Remove toast
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => container.removeChild(toast), 300);
        }, duration);
    }
    
    showModal(title, content, graveData = null) {
        const modal = document.getElementById('grave-modal');
        const modalTitle = document.getElementById('modal-title');
        const modalBody = document.getElementById('modal-body');
        
        modalTitle.textContent = title;
        modalBody.innerHTML = content;
        
        if (graveData) {
            modal.dataset.graveData = JSON.stringify(graveData);
            document.getElementById('navigate-btn').style.display = 'block';
        } else {
            document.getElementById('navigate-btn').style.display = 'none';
        }
        
        modal.classList.add('active');
    }
    
    closeModal() {
        document.getElementById('grave-modal').classList.remove('active');
    }
    
    formatGraveDetails(grave) {
        return `
            <div class="grave-details">
                <div class="detail-row">
                    <strong>Grave Number:</strong> ${grave.grave_number}
                </div>
                <div class="detail-row">
                    <strong>Section:</strong> ${grave.section.name} (${grave.section.code})
                </div>
                ${grave.birth_date ? `<div class="detail-row"><strong>Birth Date:</strong> ${grave.birth_date}</div>` : ''}
                ${grave.death_date ? `<div class="detail-row"><strong>Death Date:</strong> ${grave.death_date}</div>` : ''}
                ${grave.burial_date ? `<div class="detail-row"><strong>Burial Date:</strong> ${grave.burial_date}</div>` : ''}
                ${grave.plot_size ? `<div class="detail-row"><strong>Plot Size:</strong> ${grave.plot_size}</div>` : ''}
                <div class="detail-row">
                    <strong>Grave Type:</strong> ${this.formatGraveType(grave.grave_type)}
                </div>
                ${grave.distance_meters ? `<div class="detail-row"><strong>Distance:</strong> ${this.formatDistance(grave.distance_meters)}</div>` : ''}
                ${grave.notes ? `<div class="detail-row"><strong>Notes:</strong> ${grave.notes}</div>` : ''}
                ${grave.photo_url ? `<div class="detail-row"><img src="${grave.photo_url}" alt="Grave photo" style="max-width: 100%; border-radius: 8px; margin-top: 1rem;"></div>` : ''}
            </div>
        `;
    }
    
    formatGraveType(type) {
        const types = {
            'regular': 'Regular Grave',
            'family': 'Family Plot',
            'mausoleum': 'Mausoleum',
            'columbarium': 'Columbarium'
        };
        return types[type] || type;
    }
    
    formatDistance(meters) {
        if (meters < 1000) {
            return `${Math.round(meters)}m away`;
        } else {
            return `${(meters / 1000).toFixed(1)}km away`;
        }
    }
    
    navigateToGrave(latitude, longitude) {
        if (this.userLocation) {
            // Use Google Maps for navigation
            const url = `https://www.google.com/maps/dir/${this.userLocation.latitude},${this.userLocation.longitude}/${latitude},${longitude}`;
            window.open(url, '_blank');
        } else {
            // Just show the location
            const url = `https://www.google.com/maps?q=${latitude},${longitude}`;
            window.open(url, '_blank');
        }
        
        this.closeModal();
        this.showToast('Opening navigation...', 'success');
    }
    
    async makeAPIRequest(endpoint, options = {}) {
        const baseURL = window.location.origin + '/ngeks/api';
        const url = `${baseURL}${endpoint}`;

        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const finalOptions = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'API request failed');
            }

            return data;
        } catch (error) {
            console.error('API Error:', error);
            this.showToast(`Error: ${error.message}`, 'error');
            throw error;
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.cemeteryApp = new CemeteryApp();
});

// Add some utility functions to the global scope
window.showGraveDetails = function(graveId) {
    // This will be called from search results
    const grave = window.cemeteryApp.graves.find(g => g.id === graveId);
    if (grave) {
        const content = window.cemeteryApp.formatGraveDetails(grave);
        window.cemeteryApp.showModal(grave.deceased_name, content, grave);
    }
};

// Handle online/offline status
window.addEventListener('online', () => {
    window.cemeteryApp.showToast('Connection restored', 'success');
});

window.addEventListener('offline', () => {
    window.cemeteryApp.showToast('You are offline. Some features may be limited.', 'warning');
});

// Add CSS for grave details
const style = document.createElement('style');
style.textContent = `
    .grave-details {
        font-size: 0.95rem;
    }
    
    .detail-row {
        margin-bottom: 0.75rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #ecf0f1;
    }
    
    .detail-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .detail-row strong {
        color: #2c3e50;
        display: inline-block;
        min-width: 100px;
    }
`;
document.head.appendChild(style);
