/**
 * Map functionality for Cemetery App
 */

class MapManager {
    constructor(app) {
        this.app = app;
        this.map = null;
        this.markers = [];
        this.userMarker = null;
        this.directionsService = null;
        this.directionsRenderer = null;
        this.infoWindow = null;
        
        // Cemetery center coordinates (Sagay City, Negros Occidental)
        this.cemeteryCenter = {
            lat: 10.8967,
            lng: 123.4167
        };
        
        this.init();
    }
    
    init() {
        this.bindEvents();
    }
    
    bindEvents() {
        // Map control buttons
        document.getElementById('locate-btn').addEventListener('click', () => {
            this.locateUser();
        });
        
        document.getElementById('cemetery-center-btn').addEventListener('click', () => {
            this.centerOnCemetery();
        });
    }
    
    async initializeMap() {
        if (this.map) return; // Already initialized
        
        try {
            // Wait for Google Maps to load
            await this.waitForGoogleMaps();
            
            const mapContainer = document.getElementById('map-container');
            
            this.map = new google.maps.Map(mapContainer, {
                center: this.cemeteryCenter,
                zoom: 18,
                mapTypeId: google.maps.MapTypeId.SATELLITE,
                mapTypeControl: true,
                mapTypeControlOptions: {
                    style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
                    position: google.maps.ControlPosition.TOP_CENTER,
                    mapTypeIds: [
                        google.maps.MapTypeId.ROADMAP,
                        google.maps.MapTypeId.SATELLITE,
                        google.maps.MapTypeId.HYBRID
                    ]
                },
                zoomControl: true,
                zoomControlOptions: {
                    position: google.maps.ControlPosition.RIGHT_BOTTOM
                },
                streetViewControl: true,
                streetViewControlOptions: {
                    position: google.maps.ControlPosition.RIGHT_BOTTOM
                },
                fullscreenControl: true,
                gestureHandling: 'greedy'
            });
            
            // Initialize directions service
            this.directionsService = new google.maps.DirectionsService();
            this.directionsRenderer = new google.maps.DirectionsRenderer({
                draggable: false,
                panel: null
            });
            this.directionsRenderer.setMap(this.map);
            
            // Initialize info window
            this.infoWindow = new google.maps.InfoWindow();
            
            // Load cemetery sections and graves
            await this.loadCemeterySections();
            await this.loadGraveMarkers();
            
            // Add user location if available
            if (this.app.userLocation) {
                this.addUserMarker();
            }
            
            this.app.showToast('Map loaded successfully', 'success');
            
        } catch (error) {
            console.error('Map initialization failed:', error);
            this.app.showToast('Failed to load map', 'error');
        }
    }
    
    waitForGoogleMaps() {
        return new Promise((resolve, reject) => {
            if (window.google && window.google.maps) {
                resolve();
                return;
            }
            
            let attempts = 0;
            const maxAttempts = 50;
            
            const checkGoogle = () => {
                attempts++;
                if (window.google && window.google.maps) {
                    resolve();
                } else if (attempts >= maxAttempts) {
                    reject(new Error('Google Maps failed to load'));
                } else {
                    setTimeout(checkGoogle, 100);
                }
            };
            
            checkGoogle();
        });
    }
    
    async loadCemeterySections() {
        try {
            const response = await this.app.makeAPIRequest('/sections');
            
            if (response.success && response.sections) {
                response.sections.forEach(section => {
                    this.addSectionMarker(section);
                });
            }
        } catch (error) {
            console.error('Failed to load cemetery sections:', error);
        }
    }
    
    async loadGraveMarkers() {
        try {
            const response = await this.app.makeAPIRequest('/graves?limit=100');
            
            if (response.success && response.graves) {
                response.graves.forEach(grave => {
                    this.addGraveMarker(grave);
                });
            }
        } catch (error) {
            console.error('Failed to load grave markers:', error);
        }
    }
    
    addSectionMarker(section) {
        const marker = new google.maps.Marker({
            position: {
                lat: section.coordinates.latitude,
                lng: section.coordinates.longitude
            },
            map: this.map,
            title: section.section_name,
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="15" cy="15" r="12" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                        <text x="15" y="19" text-anchor="middle" fill="white" font-size="10" font-weight="bold">${section.section_code}</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(30, 30),
                anchor: new google.maps.Point(15, 15)
            }
        });
        
        const infoContent = `
            <div class="info-window">
                <h3>${section.section_name}</h3>
                <p><strong>Code:</strong> ${section.section_code}</p>
                <p><strong>Graves:</strong> ${section.grave_count}</p>
                ${section.description ? `<p>${section.description}</p>` : ''}
            </div>
        `;
        
        marker.addListener('click', () => {
            this.infoWindow.setContent(infoContent);
            this.infoWindow.open(this.map, marker);
        });
        
        this.markers.push(marker);
    }
    
    addGraveMarker(grave) {
        const marker = new google.maps.Marker({
            position: {
                lat: grave.coordinates.latitude,
                lng: grave.coordinates.longitude
            },
            map: this.map,
            title: `${grave.deceased_name} (${grave.grave_number})`,
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="10" cy="10" r="8" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                        <path d="M6 10 L14 10 M10 6 L10 14" stroke="white" stroke-width="2"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(20, 20),
                anchor: new google.maps.Point(10, 10)
            }
        });
        
        const infoContent = `
            <div class="info-window">
                <h3>${grave.deceased_name}</h3>
                <p><strong>Grave #:</strong> ${grave.grave_number}</p>
                <p><strong>Section:</strong> ${grave.section.name}</p>
                ${grave.death_date ? `<p><strong>Death Date:</strong> ${grave.death_date}</p>` : ''}
                <div class="info-actions">
                    <button onclick="window.showGraveDetails(${grave.id})" class="info-btn">View Details</button>
                    <button onclick="window.cemeteryApp.mapManager.getDirections(${grave.coordinates.latitude}, ${grave.coordinates.longitude})" class="info-btn">Directions</button>
                </div>
            </div>
        `;
        
        marker.addListener('click', () => {
            this.infoWindow.setContent(infoContent);
            this.infoWindow.open(this.map, marker);
        });
        
        this.markers.push(marker);
    }
    
    addUserMarker() {
        if (!this.app.userLocation) return;
        
        if (this.userMarker) {
            this.userMarker.setMap(null);
        }
        
        this.userMarker = new google.maps.Marker({
            position: {
                lat: this.app.userLocation.latitude,
                lng: this.app.userLocation.longitude
            },
            map: this.map,
            title: 'Your Location',
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" fill="#27ae60" stroke="#229954" stroke-width="2"/>
                        <circle cx="12" cy="12" r="4" fill="white"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(24, 24),
                anchor: new google.maps.Point(12, 12)
            }
        });
    }
    
    locateUser() {
        if (!this.app.userLocation) {
            this.app.showToast('Location not available', 'warning');
            return;
        }
        
        const userPos = {
            lat: this.app.userLocation.latitude,
            lng: this.app.userLocation.longitude
        };
        
        this.map.setCenter(userPos);
        this.map.setZoom(19);
        
        this.addUserMarker();
        this.app.showToast('Centered on your location', 'success');
    }
    
    centerOnCemetery() {
        this.map.setCenter(this.cemeteryCenter);
        this.map.setZoom(18);
        this.app.showToast('Centered on cemetery', 'success');
    }
    
    getDirections(destLat, destLng) {
        if (!this.app.userLocation) {
            this.app.showToast('Location not available for directions', 'warning');
            return;
        }
        
        const origin = {
            lat: this.app.userLocation.latitude,
            lng: this.app.userLocation.longitude
        };
        
        const destination = {
            lat: destLat,
            lng: destLng
        };
        
        const request = {
            origin: origin,
            destination: destination,
            travelMode: google.maps.TravelMode.WALKING,
            unitSystem: google.maps.UnitSystem.METRIC
        };
        
        this.directionsService.route(request, (result, status) => {
            if (status === 'OK') {
                this.directionsRenderer.setDirections(result);
                
                const route = result.routes[0];
                const distance = route.legs[0].distance.text;
                const duration = route.legs[0].duration.text;
                
                this.app.showToast(`Route found: ${distance}, ${duration}`, 'success');
            } else {
                console.error('Directions request failed:', status);
                this.app.showToast('Failed to get directions', 'error');
            }
        });
    }
    
    clearDirections() {
        if (this.directionsRenderer) {
            this.directionsRenderer.setDirections({routes: []});
        }
    }
    
    showGraveOnMap(graveId) {
        // Find and highlight a specific grave on the map
        const grave = this.app.graves.find(g => g.id === graveId);
        if (!grave) return;
        
        const position = {
            lat: grave.coordinates.latitude,
            lng: grave.coordinates.longitude
        };
        
        this.map.setCenter(position);
        this.map.setZoom(20);
        
        // Find the marker and trigger click
        const marker = this.markers.find(m => 
            m.getPosition().lat() === position.lat && 
            m.getPosition().lng() === position.lng
        );
        
        if (marker) {
            google.maps.event.trigger(marker, 'click');
        }
    }
}

// Initialize map manager when app is ready
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        if (window.cemeteryApp) {
            window.cemeteryApp.mapManager = new MapManager(window.cemeteryApp);
        }
    }, 100);
});

// Add map-specific styles
const mapStyles = document.createElement('style');
mapStyles.textContent = `
    .info-window {
        max-width: 250px;
        font-family: inherit;
    }
    
    .info-window h3 {
        margin: 0 0 0.5rem 0;
        color: #2c3e50;
        font-size: 1rem;
    }
    
    .info-window p {
        margin: 0.25rem 0;
        font-size: 0.9rem;
        color: #7f8c8d;
    }
    
    .info-actions {
        margin-top: 0.75rem;
        display: flex;
        gap: 0.5rem;
    }
    
    .info-btn {
        padding: 0.5rem 0.75rem;
        background: #3498db;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.8rem;
        transition: background 0.3s ease;
    }
    
    .info-btn:hover {
        background: #2980b9;
    }
`;
document.head.appendChild(mapStyles);
