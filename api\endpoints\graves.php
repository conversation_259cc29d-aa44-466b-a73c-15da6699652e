<?php
/**
 * Graves API Endpoint
 */

function handleGravesRequest($method, $id) {
    switch ($method) {
        case 'GET':
            if ($id) {
                getGraveById($id);
            } else {
                getAllGraves();
            }
            break;
            
        case 'POST':
            if (!isAdmin()) {
                sendResponse(401, ['error' => 'Unauthorized']);
            }
            createGrave();
            break;
            
        case 'PUT':
            if (!isAdmin()) {
                sendResponse(401, ['error' => 'Unauthorized']);
            }
            if (!$id) {
                sendResponse(400, ['error' => 'Grave ID is required']);
            }
            updateGrave($id);
            break;
            
        case 'DELETE':
            if (!isAdmin()) {
                sendResponse(401, ['error' => 'Unauthorized']);
            }
            if (!$id) {
                sendResponse(400, ['error' => 'Grave ID is required']);
            }
            deleteGrave($id);
            break;
            
        default:
            sendResponse(405, ['error' => 'Method not allowed']);
    }
}

function getGraveById($id) {
    try {
        $db = getDBConnection();
        
        $stmt = $db->prepare("
            SELECT 
                g.*,
                s.section_name,
                s.section_code,
                au.full_name as created_by_name
            FROM graves g
            JOIN cemetery_sections s ON g.section_id = s.id
            LEFT JOIN admin_users au ON g.created_by = au.id
            WHERE g.id = ? AND g.is_active = 1
        ");
        
        $stmt->execute([$id]);
        $grave = $stmt->fetch();
        
        if (!$grave) {
            sendResponse(404, ['error' => 'Grave not found']);
        }
        
        $result = [
            'id' => intval($grave['id']),
            'grave_number' => $grave['grave_number'],
            'deceased_name' => $grave['deceased_name'],
            'birth_date' => formatDate($grave['birth_date']),
            'death_date' => formatDate($grave['death_date']),
            'burial_date' => formatDate($grave['burial_date']),
            'coordinates' => [
                'latitude' => floatval($grave['latitude']),
                'longitude' => floatval($grave['longitude'])
            ],
            'plot_size' => $grave['plot_size'],
            'grave_type' => $grave['grave_type'],
            'status' => $grave['status'],
            'notes' => $grave['notes'],
            'family_contact' => $grave['family_contact'],
            'contact_phone' => $grave['contact_phone'],
            'section' => [
                'id' => intval($grave['section_id']),
                'name' => $grave['section_name'],
                'code' => $grave['section_code']
            ],
            'photo_url' => $grave['photo_filename'] ? UPLOAD_URL . $grave['photo_filename'] : null,
            'qr_code' => $grave['qr_code'],
            'created_by' => $grave['created_by_name'],
            'created_at' => $grave['created_at'],
            'updated_at' => $grave['updated_at']
        ];
        
        sendResponse(200, ['success' => true, 'grave' => $result]);
        
    } catch (Exception $e) {
        sendResponse(500, ['error' => 'Failed to retrieve grave: ' . $e->getMessage()]);
    }
}

function getAllGraves() {
    try {
        $db = getDBConnection();
        
        $limit = min(intval($_GET['limit'] ?? 20), 100);
        $offset = intval($_GET['offset'] ?? 0);
        $section_id = $_GET['section_id'] ?? null;
        
        $where_clause = 'g.is_active = 1';
        $params = [];
        
        if ($section_id) {
            $where_clause .= ' AND g.section_id = ?';
            $params[] = $section_id;
        }
        
        $stmt = $db->prepare("
            SELECT 
                g.id,
                g.grave_number,
                g.deceased_name,
                g.death_date,
                g.latitude,
                g.longitude,
                g.grave_type,
                g.status,
                s.section_name,
                s.section_code
            FROM graves g
            JOIN cemetery_sections s ON g.section_id = s.id
            WHERE {$where_clause}
            ORDER BY g.deceased_name
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt->execute($params);
        $graves = $stmt->fetchAll();
        
        $results = [];
        foreach ($graves as $grave) {
            $results[] = [
                'id' => intval($grave['id']),
                'grave_number' => $grave['grave_number'],
                'deceased_name' => $grave['deceased_name'],
                'death_date' => formatDate($grave['death_date']),
                'coordinates' => [
                    'latitude' => floatval($grave['latitude']),
                    'longitude' => floatval($grave['longitude'])
                ],
                'grave_type' => $grave['grave_type'],
                'status' => $grave['status'],
                'section' => [
                    'name' => $grave['section_name'],
                    'code' => $grave['section_code']
                ]
            ];
        }
        
        sendResponse(200, [
            'success' => true,
            'graves' => $results,
            'pagination' => [
                'limit' => $limit,
                'offset' => $offset,
                'total' => count($results)
            ]
        ]);
        
    } catch (Exception $e) {
        sendResponse(500, ['error' => 'Failed to retrieve graves: ' . $e->getMessage()]);
    }
}

function createGrave() {
    $data = getJsonInput();
    
    $required_fields = ['grave_number', 'section_id', 'deceased_name', 'latitude', 'longitude'];
    $missing_fields = validateRequiredFields($data, $required_fields);
    
    if (!empty($missing_fields)) {
        sendResponse(400, ['error' => 'Missing required fields: ' . implode(', ', $missing_fields)]);
    }
    
    // Validate GPS coordinates
    if (!validateGPSCoordinates($data['latitude'], $data['longitude'])) {
        sendResponse(400, ['error' => 'Invalid GPS coordinates']);
    }
    
    try {
        $db = getDBConnection();
        
        // Check if grave number already exists
        $stmt = $db->prepare("SELECT id FROM graves WHERE grave_number = ? AND is_active = 1");
        $stmt->execute([$data['grave_number']]);
        if ($stmt->fetch()) {
            sendResponse(400, ['error' => 'Grave number already exists']);
        }
        
        // Generate QR code
        $qr_code = generateGraveQRCode(0); // Will update with actual ID after insert
        
        $stmt = $db->prepare("
            INSERT INTO graves 
            (grave_number, section_id, deceased_name, deceased_name_search, birth_date, death_date, 
             burial_date, latitude, longitude, plot_size, grave_type, status, notes, 
             family_contact, contact_phone, qr_code, created_by) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            sanitizeString($data['grave_number']),
            intval($data['section_id']),
            sanitizeString($data['deceased_name']),
            strtolower(sanitizeString($data['deceased_name'])), // For search
            $data['birth_date'] ?? null,
            $data['death_date'] ?? null,
            $data['burial_date'] ?? null,
            floatval($data['latitude']),
            floatval($data['longitude']),
            sanitizeString($data['plot_size'] ?? ''),
            $data['grave_type'] ?? 'regular',
            $data['status'] ?? 'occupied',
            sanitizeString($data['notes'] ?? ''),
            sanitizeString($data['family_contact'] ?? ''),
            sanitizeString($data['contact_phone'] ?? ''),
            $qr_code,
            1 // Default admin user ID - should be from session
        ]);
        
        $grave_id = $db->lastInsertId();
        
        // Update QR code with actual grave ID
        $final_qr_code = generateGraveQRCode($grave_id);
        $stmt = $db->prepare("UPDATE graves SET qr_code = ? WHERE id = ?");
        $stmt->execute([$final_qr_code, $grave_id]);
        
        sendResponse(201, [
            'success' => true,
            'message' => 'Grave created successfully',
            'grave_id' => intval($grave_id),
            'qr_code' => $final_qr_code
        ]);
        
    } catch (Exception $e) {
        sendResponse(500, ['error' => 'Failed to create grave: ' . $e->getMessage()]);
    }
}

function updateGrave($id) {
    $data = getJsonInput();
    
    try {
        $db = getDBConnection();
        
        // Check if grave exists
        $stmt = $db->prepare("SELECT id FROM graves WHERE id = ? AND is_active = 1");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            sendResponse(404, ['error' => 'Grave not found']);
        }
        
        // Build update query dynamically
        $update_fields = [];
        $params = [];
        
        $allowed_fields = [
            'grave_number', 'section_id', 'deceased_name', 'birth_date', 'death_date',
            'burial_date', 'latitude', 'longitude', 'plot_size', 'grave_type',
            'status', 'notes', 'family_contact', 'contact_phone'
        ];
        
        foreach ($allowed_fields as $field) {
            if (isset($data[$field])) {
                if ($field === 'deceased_name') {
                    $update_fields[] = "deceased_name = ?, deceased_name_search = ?";
                    $params[] = sanitizeString($data[$field]);
                    $params[] = strtolower(sanitizeString($data[$field]));
                } elseif (in_array($field, ['latitude', 'longitude'])) {
                    if (!validateGPSCoordinates($data['latitude'] ?? 0, $data['longitude'] ?? 0)) {
                        sendResponse(400, ['error' => 'Invalid GPS coordinates']);
                    }
                    $update_fields[] = "{$field} = ?";
                    $params[] = floatval($data[$field]);
                } else {
                    $update_fields[] = "{$field} = ?";
                    $params[] = is_string($data[$field]) ? sanitizeString($data[$field]) : $data[$field];
                }
            }
        }
        
        if (empty($update_fields)) {
            sendResponse(400, ['error' => 'No valid fields to update']);
        }
        
        $params[] = $id;
        
        $sql = "UPDATE graves SET " . implode(', ', $update_fields) . " WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        
        sendResponse(200, [
            'success' => true,
            'message' => 'Grave updated successfully'
        ]);
        
    } catch (Exception $e) {
        sendResponse(500, ['error' => 'Failed to update grave: ' . $e->getMessage()]);
    }
}

function deleteGrave($id) {
    try {
        $db = getDBConnection();
        
        // Soft delete - set is_active to 0
        $stmt = $db->prepare("UPDATE graves SET is_active = 0 WHERE id = ?");
        $stmt->execute([$id]);
        
        if ($stmt->rowCount() === 0) {
            sendResponse(404, ['error' => 'Grave not found']);
        }
        
        sendResponse(200, [
            'success' => true,
            'message' => 'Grave deleted successfully'
        ]);
        
    } catch (Exception $e) {
        sendResponse(500, ['error' => 'Failed to delete grave: ' . $e->getMessage()]);
    }
}
?>
