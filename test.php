<?php
/**
 * System Test Script for Sagay Cemetery Grave Locator
 * Run this script to test basic functionality
 */

// Check if system is installed
if (!file_exists('config/installed.lock')) {
    die('System not installed. Please run install.php first.');
}

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

$tests = [];
$passed = 0;
$failed = 0;

// Test 1: Database Connection
try {
    $db = getDBConnection();
    if ($db) {
        $tests[] = ['name' => 'Database Connection', 'status' => 'PASS', 'message' => 'Connected successfully'];
        $passed++;
    } else {
        $tests[] = ['name' => 'Database Connection', 'status' => 'FAIL', 'message' => 'Connection failed'];
        $failed++;
    }
} catch (Exception $e) {
    $tests[] = ['name' => 'Database Connection', 'status' => 'FAIL', 'message' => $e->getMessage()];
    $failed++;
}

// Test 2: Database Tables
try {
    $db = getDBConnection();
    $required_tables = ['admin_users', 'cemetery_sections', 'graves', 'search_logs'];
    $existing_tables = [];
    
    $stmt = $db->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $existing_tables[] = $row[0];
    }
    
    $missing_tables = array_diff($required_tables, $existing_tables);
    
    if (empty($missing_tables)) {
        $tests[] = ['name' => 'Database Tables', 'status' => 'PASS', 'message' => 'All required tables exist'];
        $passed++;
    } else {
        $tests[] = ['name' => 'Database Tables', 'status' => 'FAIL', 'message' => 'Missing tables: ' . implode(', ', $missing_tables)];
        $failed++;
    }
} catch (Exception $e) {
    $tests[] = ['name' => 'Database Tables', 'status' => 'FAIL', 'message' => $e->getMessage()];
    $failed++;
}

// Test 3: Admin User Exists
try {
    $db = getDBConnection();
    $stmt = $db->query("SELECT COUNT(*) as count FROM admin_users WHERE is_active = 1");
    $result = $stmt->fetch();
    
    if ($result['count'] > 0) {
        $tests[] = ['name' => 'Admin User', 'status' => 'PASS', 'message' => 'Admin user exists'];
        $passed++;
    } else {
        $tests[] = ['name' => 'Admin User', 'status' => 'FAIL', 'message' => 'No admin user found'];
        $failed++;
    }
} catch (Exception $e) {
    $tests[] = ['name' => 'Admin User', 'status' => 'FAIL', 'message' => $e->getMessage()];
    $failed++;
}

// Test 4: File Permissions
$upload_dir = 'uploads';
if (!is_dir($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}

if (is_writable($upload_dir)) {
    $tests[] = ['name' => 'Upload Directory', 'status' => 'PASS', 'message' => 'Upload directory is writable'];
    $passed++;
} else {
    $tests[] = ['name' => 'Upload Directory', 'status' => 'FAIL', 'message' => 'Upload directory is not writable'];
    $failed++;
}

// Test 5: PHP Extensions
$required_extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
$missing_extensions = [];

foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        $missing_extensions[] = $ext;
    }
}

if (empty($missing_extensions)) {
    $tests[] = ['name' => 'PHP Extensions', 'status' => 'PASS', 'message' => 'All required extensions loaded'];
    $passed++;
} else {
    $tests[] = ['name' => 'PHP Extensions', 'status' => 'FAIL', 'message' => 'Missing extensions: ' . implode(', ', $missing_extensions)];
    $failed++;
}

// Test 6: API Endpoints
$api_tests = [
    '/api/sections' => 'Sections API',
    '/api/search?q=test' => 'Search API'
];

foreach ($api_tests as $endpoint => $name) {
    try {
        $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . $endpoint;
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'ignore_errors' => true
            ]
        ]);
        
        $response = file_get_contents($url, false, $context);
        $data = json_decode($response, true);
        
        if ($data && isset($data['success'])) {
            $tests[] = ['name' => $name, 'status' => 'PASS', 'message' => 'API endpoint responding'];
            $passed++;
        } else {
            $tests[] = ['name' => $name, 'status' => 'FAIL', 'message' => 'API endpoint not responding correctly'];
            $failed++;
        }
    } catch (Exception $e) {
        $tests[] = ['name' => $name, 'status' => 'FAIL', 'message' => $e->getMessage()];
        $failed++;
    }
}

// Test 7: Configuration
$config_issues = [];

if (!defined('GOOGLE_MAPS_API_KEY') || GOOGLE_MAPS_API_KEY === 'YOUR_GOOGLE_MAPS_API_KEY_HERE') {
    $config_issues[] = 'Google Maps API key not configured';
}

if (!defined('SITE_URL') || SITE_URL === 'http://localhost/ngeks') {
    $config_issues[] = 'Site URL may need updating for production';
}

if (empty($config_issues)) {
    $tests[] = ['name' => 'Configuration', 'status' => 'PASS', 'message' => 'Configuration looks good'];
    $passed++;
} else {
    $tests[] = ['name' => 'Configuration', 'status' => 'WARNING', 'message' => implode(', ', $config_issues)];
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Test - Sagay Cemetery</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .summary {
            padding: 1.5rem;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        .summary-item {
            flex: 1;
        }
        .summary-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .pass { color: #27ae60; }
        .fail { color: #e74c3c; }
        .warning { color: #f39c12; }
        .tests {
            padding: 1.5rem;
        }
        .test-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-status {
            width: 80px;
            text-align: center;
            font-weight: bold;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            margin-right: 1rem;
            font-size: 0.8rem;
        }
        .test-status.PASS {
            background: #d4edda;
            color: #155724;
        }
        .test-status.FAIL {
            background: #f8d7da;
            color: #721c24;
        }
        .test-status.WARNING {
            background: #fff3cd;
            color: #856404;
        }
        .test-name {
            font-weight: 600;
            margin-right: 1rem;
            min-width: 150px;
        }
        .test-message {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .actions {
            padding: 1.5rem;
            background: #f8f9fa;
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            font-weight: 500;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>System Test Results</h1>
            <p>Sagay Cemetery Grave Locator</p>
        </div>
        
        <div class="summary">
            <div class="summary-item">
                <div class="summary-number pass"><?php echo $passed; ?></div>
                <div>Passed</div>
            </div>
            <div class="summary-item">
                <div class="summary-number fail"><?php echo $failed; ?></div>
                <div>Failed</div>
            </div>
            <div class="summary-item">
                <div class="summary-number"><?php echo count($tests); ?></div>
                <div>Total Tests</div>
            </div>
        </div>
        
        <div class="tests">
            <?php foreach ($tests as $test): ?>
                <div class="test-item">
                    <div class="test-status <?php echo $test['status']; ?>">
                        <?php echo $test['status']; ?>
                    </div>
                    <div class="test-name"><?php echo htmlspecialchars($test['name']); ?></div>
                    <div class="test-message"><?php echo htmlspecialchars($test['message']); ?></div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="actions">
            <?php if ($failed === 0): ?>
                <a href="index.html" class="btn btn-success">Launch Mobile App</a>
                <a href="admin/" class="btn">Open Admin Panel</a>
            <?php else: ?>
                <a href="install.php" class="btn">Run Installer</a>
                <a href="test.php" class="btn">Rerun Tests</a>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
