/* Admin Panel Styles for Sagay Cemetery */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-gray: #ecf0f1;
    --dark-gray: #7f8c8d;
    --white: #ffffff;
    --sidebar-width: 260px;
    --header-height: 70px;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f8f9fa;
    color: var(--primary-color);
    line-height: 1.6;
}

/* Admin Container */
.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(180deg, var(--primary-color), #34495e);
    color: var(--white);
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    transition: var(--transition);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
}

.sidebar-header i {
    margin-right: 0.5rem;
    color: var(--secondary-color);
}

.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav li {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background: rgba(255,255,255,0.1);
    color: var(--white);
    border-left-color: var(--secondary-color);
}

.nav-link.active {
    background: rgba(52, 152, 219, 0.2);
    color: var(--white);
    border-left-color: var(--secondary-color);
}

.nav-link i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.admin-info {
    margin-bottom: 1rem;
}

.admin-info p {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.logout-btn {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background: var(--danger-color);
    color: var(--white);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-size: 0.9rem;
}

.logout-btn:hover {
    background: #c0392b;
}

.logout-btn i {
    margin-right: 0.5rem;
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header */
.main-header {
    height: var(--header-height);
    background: var(--white);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--primary-color);
    cursor: pointer;
    margin-right: 1rem;
    padding: 0.5rem;
}

.header-left h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-stats {
    font-size: 0.9rem;
    color: var(--dark-gray);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid var(--light-gray);
}

/* Page Content */
.page-content {
    flex: 1;
    padding: 2rem;
}

.admin-page {
    display: none;
}

.admin-page.active {
    display: block;
}

/* Dashboard */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
}

.stat-card:nth-child(1) .stat-icon { background: var(--secondary-color); }
.stat-card:nth-child(2) .stat-icon { background: var(--success-color); }
.stat-card:nth-child(3) .stat-icon { background: var(--warning-color); }
.stat-card:nth-child(4) .stat-icon { background: var(--danger-color); }

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: var(--dark-gray);
    font-size: 0.9rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.dashboard-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--light-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
}

.view-all {
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.view-all:hover {
    text-decoration: underline;
}

.card-content {
    padding: 1.5rem;
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.page-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
}

.btn-primary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-success:hover {
    background: #229954;
}

.btn-warning {
    background: var(--warning-color);
    color: var(--white);
}

.btn-warning:hover {
    background: #e67e22;
}

.btn-danger {
    background: var(--danger-color);
    color: var(--white);
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-secondary {
    background: var(--light-gray);
    color: var(--primary-color);
}

.btn-secondary:hover {
    background: var(--dark-gray);
    color: var(--white);
}

/* Filters Bar */
.filters-bar {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 2px solid var(--light-gray);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
}

.search-box input:focus {
    outline: none;
    border-color: var(--secondary-color);
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--dark-gray);
}

.filters-bar select {
    padding: 0.75rem;
    border: 2px solid var(--light-gray);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    background: var(--white);
    min-width: 150px;
}

/* Table */
.table-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--light-gray);
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.data-table td {
    font-size: 0.9rem;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.loading {
    text-align: center;
    color: var(--dark-gray);
    font-style: italic;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition);
}

.action-btn.edit {
    background: var(--warning-color);
    color: var(--white);
}

.action-btn.delete {
    background: var(--danger-color);
    color: var(--white);
}

.action-btn:hover {
    opacity: 0.8;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.pagination button {
    padding: 0.5rem 1rem;
    border: 1px solid var(--light-gray);
    background: var(--white);
    color: var(--primary-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.pagination button:hover {
    background: var(--secondary-color);
    color: var(--white);
    border-color: var(--secondary-color);
}

.pagination button.active {
    background: var(--secondary-color);
    color: var(--white);
    border-color: var(--secondary-color);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    padding: 2rem;
}

.modal.active {
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--light-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: var(--primary-color);
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--dark-gray);
    padding: 0.5rem;
}

.modal-body {
    padding: 1.5rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--light-gray);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 3000;
}

.toast {
    background: var(--primary-color);
    color: var(--white);
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow);
    transform: translateX(100%);
    transition: var(--transition);
    max-width: 300px;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background: var(--success-color);
}

.toast.error {
    background: var(--danger-color);
}

.toast.warning {
    background: var(--warning-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .page-content {
        padding: 1rem;
    }
    
    .page-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .filters-bar {
        flex-direction: column;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .data-table {
        font-size: 0.8rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .main-header {
        padding: 0 1rem;
    }
    
    .header-left h1 {
        font-size: 1.2rem;
    }
    
    .user-menu span {
        display: none;
    }
    
    .modal {
        padding: 1rem;
    }
}
