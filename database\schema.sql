-- Sagay Public Cemetery Database Schema
-- Created: 2025-07-18

CREATE DATABASE IF NOT EXISTS sagay_cemetery CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE sagay_cemetery;

-- Admin users table
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('super_admin', 'admin', 'operator') DEFAULT 'operator',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Cemetery sections/blocks table
CREATE TABLE cemetery_sections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    section_name <PERSON><PERSON>HA<PERSON>(50) NOT NULL,
    section_code VARCHAR(10) UNIQUE NOT NULL,
    description TEXT,
    center_latitude DECIMAL(10, 8) NOT NULL,
    center_longitude DECIMAL(11, 8) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Graves table
CREATE TABLE graves (
    id INT PRIMARY KEY AUTO_INCREMENT,
    grave_number VARCHAR(20) NOT NULL,
    section_id INT NOT NULL,
    deceased_name VARCHAR(100) NOT NULL,
    deceased_name_search VARCHAR(100) NOT NULL, -- For search optimization
    birth_date DATE,
    death_date DATE,
    burial_date DATE,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    plot_size VARCHAR(20),
    grave_type ENUM('regular', 'family', 'mausoleum', 'columbarium') DEFAULT 'regular',
    status ENUM('occupied', 'reserved', 'available') DEFAULT 'occupied',
    notes TEXT,
    family_contact VARCHAR(100),
    contact_phone VARCHAR(20),
    photo_filename VARCHAR(255),
    qr_code VARCHAR(100) UNIQUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (section_id) REFERENCES cemetery_sections(id),
    FOREIGN KEY (created_by) REFERENCES admin_users(id),
    INDEX idx_deceased_name (deceased_name_search),
    INDEX idx_grave_number (grave_number),
    INDEX idx_section (section_id),
    INDEX idx_coordinates (latitude, longitude)
);

-- Search logs table for analytics
CREATE TABLE search_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    search_query VARCHAR(255) NOT NULL,
    search_type ENUM('name', 'grave_number', 'qr_code') NOT NULL,
    results_found INT DEFAULT 0,
    user_ip VARCHAR(45),
    user_agent TEXT,
    device_type ENUM('mobile', 'tablet', 'desktop') DEFAULT 'mobile',
    location_latitude DECIMAL(10, 8),
    location_longitude DECIMAL(11, 8),
    search_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Visitor feedback table
CREATE TABLE visitor_feedback (
    id INT PRIMARY KEY AUTO_INCREMENT,
    grave_id INT,
    rating INT CHECK (rating >= 1 AND rating <= 5),
    feedback_text TEXT,
    visitor_name VARCHAR(100),
    visitor_email VARCHAR(100),
    is_approved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (grave_id) REFERENCES graves(id)
);

-- System settings table
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (updated_by) REFERENCES admin_users(id)
);

-- Insert default admin user (password: admin123)
INSERT INTO admin_users (username, email, password_hash, full_name, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'super_admin');

-- Insert default cemetery sections
INSERT INTO cemetery_sections (section_name, section_code, description, center_latitude, center_longitude) VALUES 
('Section A', 'SEC-A', 'Main entrance section', 10.8967, 123.4167),
('Section B', 'SEC-B', 'Eastern section', 10.8970, 123.4170),
('Section C', 'SEC-C', 'Western section', 10.8964, 123.4164),
('Mausoleum Area', 'MAU-1', 'Premium mausoleum section', 10.8965, 123.4168);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES 
('site_maintenance', 'false', 'Enable/disable maintenance mode'),
('max_search_results', '50', 'Maximum number of search results to display'),
('enable_visitor_feedback', 'true', 'Allow visitors to leave feedback'),
('cemetery_operating_hours', '6:00 AM - 6:00 PM', 'Cemetery operating hours'),
('contact_phone', '+63-34-XXX-XXXX', 'Cemetery contact phone number'),
('contact_email', '<EMAIL>', 'Cemetery contact email');
