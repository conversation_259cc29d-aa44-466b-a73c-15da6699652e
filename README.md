# Sagay Public Cemetery Grave Locator System

A comprehensive grave locator system that helps visitors find graves using GPS technology and provides administrators with management tools.

## Features

### Mobile App (For Visitors)
- Search graves by name, date, or plot number
- GPS navigation to exact grave location
- Interactive cemetery map
- Offline capability for basic searches
- QR code scanning for quick access

### Admin Web Panel
- Add, edit, and delete grave records
- Upload and manage grave photos
- Monitor search statistics
- User activity tracking
- Bulk import/export functionality
- Cemetery map management

## Technology Stack
- **Backend**: PHP 8+ with MySQL
- **Frontend**: HTML5, CSS3, JavaScript (PWA)
- **Maps**: Google Maps API / OpenStreetMap
- **Database**: MySQL
- **Server**: Apache (XAMPP compatible)

## Installation

1. Clone this repository to your XAMPP htdocs folder
2. Import the database schema from `database/schema.sql`
3. Configure database connection in `config/database.php`
4. Set up Google Maps API key in `config/config.php`
5. Access the system at `http://localhost/ngeks`

## Directory Structure

```
ngeks/
├── admin/              # Admin panel
├── api/               # REST API endpoints
├── assets/            # CSS, JS, images
├── config/            # Configuration files
├── database/          # Database schema and migrations
├── includes/          # PHP includes and utilities
├── mobile/            # Mobile web app
└── uploads/           # Uploaded images
```

## API Endpoints

- `GET /api/graves/search` - Search graves
- `GET /api/graves/{id}` - Get grave details
- `POST /api/graves` - Add new grave (admin)
- `PUT /api/graves/{id}` - Update grave (admin)
- `DELETE /api/graves/{id}` - Delete grave (admin)
- `GET /api/stats` - Get system statistics (admin)

## License

MIT License - See LICENSE file for details
