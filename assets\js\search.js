/**
 * Search functionality for Cemetery App
 */

class SearchManager {
    constructor(app) {
        this.app = app;
        this.searchTimeout = null;
        this.currentQuery = '';
        this.currentResults = [];
        
        this.init();
    }
    
    init() {
        this.bindEvents();
    }
    
    bindEvents() {
        const searchInput = document.getElementById('search-input');
        const searchBtn = document.getElementById('search-btn');
        const searchType = document.getElementById('search-type');
        
        // Search button click
        searchBtn.addEventListener('click', () => {
            this.performSearch();
        });
        
        // Enter key in search input
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });
        
        // Real-time search (debounced)
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }
            
            if (query.length >= 2) {
                this.searchTimeout = setTimeout(() => {
                    this.performSearch(query);
                }, 500);
            } else if (query.length === 0) {
                this.clearResults();
            }
        });
        
        // Search type change
        searchType.addEventListener('change', () => {
            if (this.currentQuery) {
                this.performSearch();
            }
        });
    }
    
    async performSearch(query = null) {
        const searchInput = document.getElementById('search-input');
        const searchType = document.getElementById('search-type');
        const resultsContainer = document.getElementById('search-results');
        const noResults = document.getElementById('no-results');
        
        query = query || searchInput.value.trim();
        
        if (!query) {
            this.app.showToast('Please enter a search term', 'warning');
            return;
        }
        
        this.currentQuery = query;
        
        // Show loading state
        resultsContainer.innerHTML = '<div class="loading-results">Searching...</div>';
        noResults.style.display = 'none';
        
        try {
            // Build search parameters
            const params = new URLSearchParams({
                q: query,
                type: searchType.value,
                limit: 20
            });
            
            // Add user location if available
            if (this.app.userLocation) {
                params.append('lat', this.app.userLocation.latitude);
                params.append('lng', this.app.userLocation.longitude);
            }
            
            const response = await this.app.makeAPIRequest(`/search?${params.toString()}`);
            
            if (response.success && response.results.length > 0) {
                this.displayResults(response.results);
                this.currentResults = response.results;
                this.app.graves = response.results; // Store for modal access
            } else {
                this.showNoResults();
            }
            
        } catch (error) {
            console.error('Search error:', error);
            resultsContainer.innerHTML = '<div class="error-message">Search failed. Please try again.</div>';
        }
    }
    
    displayResults(results) {
        const resultsContainer = document.getElementById('search-results');
        const noResults = document.getElementById('no-results');
        
        noResults.style.display = 'none';
        
        const html = results.map(grave => this.createGraveCard(grave)).join('');
        resultsContainer.innerHTML = html;
        
        // Add click handlers to grave cards
        resultsContainer.querySelectorAll('.grave-card').forEach(card => {
            card.addEventListener('click', () => {
                const graveId = parseInt(card.dataset.graveId);
                window.showGraveDetails(graveId);
            });
        });
    }
    
    createGraveCard(grave) {
        const distanceText = grave.distance_meters ? 
            `<span><strong>Distance:</strong> ${this.app.formatDistance(grave.distance_meters)}</span>` : '';
        
        const photoHtml = grave.photo_url ? 
            `<div class="grave-photo"><img src="${grave.photo_url}" alt="Grave photo" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px; float: right;"></div>` : '';
        
        return `
            <div class="grave-card" data-grave-id="${grave.id}">
                ${photoHtml}
                <h3>${grave.deceased_name}</h3>
                <div class="grave-info">
                    <span><strong>Grave #:</strong> ${grave.grave_number}</span>
                    <span><strong>Section:</strong> ${grave.section.name}</span>
                    ${grave.death_date ? `<span><strong>Death Date:</strong> ${grave.death_date}</span>` : ''}
                    <span><strong>Type:</strong> ${this.app.formatGraveType(grave.grave_type)}</span>
                    ${distanceText}
                </div>
                <div class="grave-actions">
                    <button class="btn btn-primary" onclick="event.stopPropagation(); window.showGraveDetails(${grave.id})">
                        View Details
                    </button>
                    <button class="btn btn-secondary" onclick="event.stopPropagation(); window.cemeteryApp.navigateToGrave(${grave.coordinates.latitude}, ${grave.coordinates.longitude})">
                        Navigate
                    </button>
                </div>
            </div>
        `;
    }
    
    showNoResults() {
        const resultsContainer = document.getElementById('search-results');
        const noResults = document.getElementById('no-results');
        
        resultsContainer.innerHTML = '';
        noResults.style.display = 'block';
    }
    
    clearResults() {
        const resultsContainer = document.getElementById('search-results');
        const noResults = document.getElementById('no-results');
        
        resultsContainer.innerHTML = '';
        noResults.style.display = 'none';
        this.currentResults = [];
        this.currentQuery = '';
    }
    
    // Quick search by QR code
    async searchByQRCode(qrCode) {
        try {
            const params = new URLSearchParams({
                q: qrCode,
                type: 'qr_code',
                limit: 1
            });
            
            if (this.app.userLocation) {
                params.append('lat', this.app.userLocation.latitude);
                params.append('lng', this.app.userLocation.longitude);
            }
            
            const response = await this.app.makeAPIRequest(`/search?${params.toString()}`);
            
            if (response.success && response.results.length > 0) {
                const grave = response.results[0];
                const content = this.app.formatGraveDetails(grave);
                this.app.showModal(grave.deceased_name, content, grave);
                return true;
            } else {
                this.app.showToast('QR code not found', 'error');
                return false;
            }
            
        } catch (error) {
            console.error('QR search error:', error);
            this.app.showToast('QR code search failed', 'error');
            return false;
        }
    }
}

// Initialize search manager when app is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait for app to be initialized
    setTimeout(() => {
        if (window.cemeteryApp) {
            window.cemeteryApp.searchManager = new SearchManager(window.cemeteryApp);
        }
    }, 100);
});

// Add loading and error styles
const searchStyles = document.createElement('style');
searchStyles.textContent = `
    .loading-results {
        text-align: center;
        padding: 2rem;
        color: #7f8c8d;
        font-style: italic;
    }
    
    .loading-results::after {
        content: '';
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid #ecf0f1;
        border-top: 2px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-left: 10px;
    }
    
    .error-message {
        text-align: center;
        padding: 2rem;
        color: #e74c3c;
        background: #fdf2f2;
        border-radius: 8px;
        border: 1px solid #fecaca;
    }
    
    .grave-photo {
        margin-bottom: 1rem;
    }
    
    .grave-photo img {
        border: 2px solid #ecf0f1;
    }
    
    @media (max-width: 480px) {
        .grave-photo {
            float: none !important;
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .grave-photo img {
            width: 80px !important;
            height: 80px !important;
        }
    }
`;
document.head.appendChild(searchStyles);
